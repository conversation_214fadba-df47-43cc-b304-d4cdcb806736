FROM golang:latest AS builder
WORKDIR /src/
COPY go.mod go.sum /src/
RUN go mod download
RUN go mod verify
# Copy the rest of the source code
COPY . /src/
ARG VERSION COMMIT BRANCH BUILD GOVERSION
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags "-X github.com/probits-as/erv-go-lonn-exporter/internal/app.Version=${VERSION} \
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.Commit=${COMMIT} \
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.Branch=${BRANCH} \
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.Build=${BUILD} \
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.GoVersion=${GOVERSION}"  -o /bin/erv-go-lonn-exporter .

FROM gcr.io/distroless/static:nonroot
WORKDIR /app/
COPY --from=builder /bin/erv-go-lonn-exporter .
EXPOSE 8080
ENTRYPOINT ["/app/erv-go-lonn-exporter"]
CMD ["-h"]
