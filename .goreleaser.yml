project_name: erv-go-lonn-exporter
before:
  hooks:
    - go mod tidy
    - go generate ./...
builds:
  - id: linux
    goos:
      - linux
    goarch:
      - amd64
    env:
      - CGO_ENABLED=0
    ldflags:
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.Version={{ .Tag }}"
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.Commit={{ .FullCommit }}"
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.Branch={{ .Branch }}"
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.Build={{ .Date }}"
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.GoVersion={{ .Env.GOVERSION }}"
  - id: windows
    goos:
      - windows
    goarch:
      - amd64
    env:
      - CGO_ENABLED=0
    ldflags:
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.Version={{ .Tag }}"
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.Commit={{ .FullCommit }}"
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.Branch={{ .Branch }}"
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.Build={{ .Date }}"
      - -X "github.com/probits-as/{{ .ProjectName }}/internal/app.GoVersion={{ .Env.GOVERSION }}"
    # Windows specific settings
    hooks:
      post: upx "{{ .Path }}" # Compress the binary
    binary: "{{ .ProjectName }}"
dockers:
  - goos: linux
    goarch: amd64
    use: buildx
    ids:
      - linux
    image_templates:
      - "cr.pbtech.no/erv/{{ .ProjectName }}:{{ .Tag }}-amd64"
      - "cr.pbtech.no/erv/{{ .ProjectName }}:v{{ .Major }}-amd64"
      - "cr.pbtech.no/erv/{{ .ProjectName }}:v{{ .Major }}.{{ .Minor }}-amd64"
      - "cr.pbtech.no/erv/{{ .ProjectName }}:latest-amd64"
    skip_push: false
    dockerfile: docker/Dockerfile.goreleaser
    build_flag_templates:
      - --platform=linux/amd64
      - --label=org.opencontainers.image.title={{ .ProjectName }}
      - --label=org.opencontainers.image.description={{ .ProjectName }}
      - --label=org.opencontainers.image.url=https://github.com/probits-as/{{ .ProjectName }}
      - --label=org.opencontainers.image.source=https://github.com/probits-as/{{ .ProjectName }}
      - --label=org.opencontainers.image.Tag={{ .Tag }}
      - --label=org.opencontainers.image.created={{ time "2006-01-02T15:04:05Z07:00" }}
      - --label=org.opencontainers.image.revision={{ .FullCommit }}
      - --label=org.opencontainers.image.licenses=Apache-2.0
docker_manifests:
  - name_template: cr.pbtech.no/erv/{{ .ProjectName }}:{{ .Tag }}
    image_templates:
      - cr.pbtech.no/erv/{{ .ProjectName }}:{{ .Tag }}-amd64
  - name_template: cr.pbtech.no/erv/{{ .ProjectName }}:v{{ .Major }}
    image_templates:
      - cr.pbtech.no/erv/{{ .ProjectName }}:v{{ .Major }}-amd64
  - name_template: cr.pbtech.no/erv/{{ .ProjectName }}:v{{ .Major }}.{{ .Minor }}
    image_templates:
      - cr.pbtech.no/erv/{{ .ProjectName }}:v{{ .Major }}.{{ .Minor }}-amd64
  - name_template: cr.pbtech.no/erv/{{ .ProjectName }}:latest
    image_templates:
      - cr.pbtech.no/erv/{{ .ProjectName }}:{{ .Tag }}-amd64
archives:
  - id: default
    builds:
      - linux
      - windows
    name_template: "{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    format_overrides:
      - goos: windows
        format: zip
checksum:
  name_template: "checksums.txt"
snapshot:
  name_template: "{{ .Tag }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"
