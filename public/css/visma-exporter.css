/* Sticky footer styles
-------------------------------------------------- */
html {
  position: relative;
  min-height: 100%;
}

body {
  /* Margin bottom by footer height */
  margin-bottom: 60px;
  /* Ensure content doesn't overlap with fixed navbar */
  padding-top: 90px;
}

.navbar {
  min-height: 90px;
  background-color: #37424f;
}

.navbar-brand {
  padding: 0 15px;
  height: 90px;
  line-height: 90px;
}

.navbar-toggle {
  /* (80px - button height 34px) / 2 = 23px */
  margin-top: 23px;
  padding: 9px 10px !important;
}

@media (min-width: 768px) {
  .navbar-nav > li > a {
    /* (80px - line-height of 27px) / 2 = 26.5px */
    padding-top: 26.5px;
    padding-bottom: 26.5px;
    line-height: 27px;
  }
}

.navbar-custom {
  line-height: 90px; /* Vertically center the text there */
}

.footer {
  /* position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  min-height: 40px;
  max-height: 40px;
  line-height: 40px; /* Vertically center the text there */
  /*background-color: rgba(26, 39, 53, 0.87);*/
}

.brand-img {
  max-height: 60px;
}

.text-white {
  color: #fff !important;
}

.modal-dialog.full-screen {
  position: fixed;
  width: auto; /* uncomment to make the width based on the left/right attributes.*/
  margin: auto;
  left: 0px;
  right: 0px;
  top: 0px;
  bottom: 0px;
}

.modal-dialog.full-screen .modal-content {
  position: absolute;
  left: 10px;
  right: 10px;
  top: 10px;
  bottom: 10px;
}

.modal-dialog.full-screen .modal-content .modal-header {
  height: 55px;
}

.modal-dialog.full-screen .modal-content .modal-body {
  overflow-y: auto;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin-top: 55px; /* .modal-header height */
  margin-bottom: 80px; /* .modal-footer height */
}

.modal-dialog.full-screen .modal-content .modal-footer {
  height: 80px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.btn-primary {
  color: #fff;
  background-color: #37424f;
  border-color: #2e6da4;
}
.btn-primary:focus,
.btn-primary.focus {
  color: #fff;
  background-color: #286090;
  border-color: #122b40;
}
.btn-primary:hover {
  color: #fff;
  background-color: #286090;
  border-color: #204d74;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: #fff;
  background-color: #286090;
  border-color: #204d74;
}
.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus {
  color: #fff;
  background-color: #204d74;
  border-color: #122b40;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  background-image: none;
}
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus {
  background-color: #37424f;
  border-color: #2e6da4;
}
.btn-primary .badge {
  color: #37424f;
  background-color: #fff;
}

/* Start by setting display:none to make this hidden.
   Then we position it in relation to the viewport window
   with position:fixed. Width, height, top and left speak
   for themselves. Background we set to 80% white with
   our animation centered, and no-repeating */
.loading {
  display: none;
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(255, 255, 255, 0.8) url("/public/img/ajax-loader.gif") 50%
    50% no-repeat;
}

/* When the body has the loading class, we turn
   the scrollbar off with overflow:hidden */
body.loading {
  overflow: hidden;
}

/* Anytime the body has the loading class, our
   modal element will be visible */
body.loading .loading {
  display: block;
}

/* Custom page CSS
-------------------------------------------------- */
/* Not required for template or sticky footer method. */

body > .container {
  padding: 15px 15px 0;
}

/* Ensure consistent padding on all screen sizes */
.container {
  padding-left: 15px;
  padding-right: 15px;
}

/* Force container padding on mobile - override Bootstrap defaults */
@media (max-width: 768px) {
  .container {
    width: auto !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }
}

/* Fix for navbar brand alignment */
.navbar-brand {
  padding: 15px;
  height: auto;
  line-height: normal;
}

.navbar-brand img {
  height: 60px;
  width: auto;
}

/* User info styling in navbar */
.navbar-nav > li.navbar-text {
  padding: 15px;
  margin: 0;
  line-height: 20px;
}

/* Inline logout button within navbar text */
.navbar-text a.logout-btn {
  color: #fff !important;
  text-decoration: none;
  padding: 5px 10px;
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
}

.navbar-text a.logout-btn:hover {
  color: #fff !important;
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  text-decoration: none;
}

/* Ensure navbar items are properly aligned */
.navbar-right {
  float: right !important;
}

.navbar-nav {
  margin: 0;
}

/* Alert positioning fix */
.alert {
  margin-top: 15px;
  margin-bottom: 15px;
}

/* Responsive navbar text */
@media (max-width: 767px) {
  .navbar-nav .navbar-text {
    padding: 10px 15px;
    margin: 0;
    text-align: center;
  }

  /* Add proper padding for collapsed navbar content */
  .navbar-collapse {
    padding-left: 15px;
    padding-right: 15px;
  }

  /* Stack user info and logout button vertically on mobile */
  .navbar-text a.logout-btn {
    display: block;
    margin: 10px auto 0;
    text-align: center;
    width: auto;
    max-width: 120px;
  }

  /* Ensure main content has proper padding on mobile */
  body > .container {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  /* Additional padding for container content */
  .container {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  /* Ensure form elements don't touch edges */
  .form-group {
    padding-left: 0;
    padding-right: 0;
  }

  /* Fix for Bootstrap responsive utilities */
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}

/* Additional mobile fixes for very small screens */
@media (max-width: 480px) {
  body > .container {
    padding-left: 10px;
    padding-right: 10px;
  }

  .container {
    padding-left: 10px;
    padding-right: 10px;
  }

  /* Ensure form elements have proper spacing */
  .form-group {
    margin-left: 0;
    margin-right: 0;
  }

  .row {
    margin-left: -5px;
    margin-right: -5px;
  }

  .row > [class*="col-"] {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.footer > .container {
  padding-right: 15px;
  padding-left: 15px;
}

code {
  font-size: 80%;
}
