{{define "title"}}
ERV Teknikk AS - Innlogging
{{end}}

{{define "body"}}

<body class="login-page">
  <!-- Fixed navbar -->
  <nav class="navbar navbar-inverse navbar-fixed-top navbar-custom">
    <div class="container-fluid">
      <div class="navbar-header">
        <a class="navbar-brand" href="#">
          <img class="brand-img" src="/public/img/brand-logo.png" alt="ERV-logo">
        </a>
      </div>
    </div>
  </nav>

  <!-- Begin page content -->
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <div class="login-container">

          <!-- Page heading -->
          <div class="text-center mb-4">
            <h1 class="login-title">Velkommen til ERV Lønn Exporter</h1>
            <p class="lead">Logg inn for å eksportere arbeidstid fra Visma Business til Visma Lønn</p>
          </div>

          <!-- Email login form -->
          <form action="/auth/login" method="POST">
            <!-- Container for both email and button with exact same width -->
            <div style="max-width: 400px; margin: 0 auto;">

              <!-- Email input -->
              <div class="form-group">
                <label for="email" class="sr-only">E-postadresse</label>
                <input type="email" class="form-control form-control-lg" id="email" name="email"
                  placeholder="📧 <EMAIL>" required autocomplete="email"
                  style="font-size: 16px; padding: 12px;">
                <small class="form-text text-muted text-center d-block mt-2">
                  Vi bruker din e-postadresse for å finne riktig innloggingssystem.
                </small>
              </div>

              <!-- Submit button -->
              <div class="form-group">
                <button type="submit" class="btn btn-primary btn-lg btn-block">
                  <i class="fa fa-sign-in" aria-hidden="true"></i>
                  Fortsett til innlogging
                </button>
              </div>

            </div>
          </form>

          <!-- Error message (server-side) -->
          {{if .ErrorMessage}}
          <div class="alert alert-danger" style="max-width: 400px; margin: 20px auto 0;">
            <h5>Feil ved innlogging</h5>
            <p>{{.ErrorMessage}}</p>
          </div>
          {{end}}

        </div>
      </div>
    </div>
  </div>

  <!-- Begin footer-->
  <footer class="navbar navbar-fixed-bottom footer">
    <div class="row">
      <div class="container">
        <div class="col col-xs-12 text-center">
          <span class="text-muted">ERV Teknikk AS - Lønn exporter - {{ .Version }}</span>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap core JavaScript -->
  <script src="/public/vendor/jquery/jquery-2.2.4.min.js"></script>
  <script src="/public/vendor/bootstrap/js/bootstrap.min.js"></script>

  <!-- Auto-focus email input -->
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const emailInput = document.getElementById('email');
      if (emailInput) {
        emailInput.focus();
      }
    });
  </script>

  <style>
    .login-page {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }

    .login-container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 10px;
      padding: 40px;
      margin-top: 100px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
    }

    .login-title {
      color: #333;
      font-weight: 300;
      margin-bottom: 10px;
    }

    .form-control-lg {
      border-radius: 8px;
      border: 2px solid #e1e5e9;
      transition: all 0.3s ease;
    }

    .form-control-lg:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .navbar-custom {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
    }

    .footer {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
    }
  </style>

</body>
{{end}}