{{define "title"}}
ERV Teknikk AS - Lønn exporter
{{end}}

{{define "body"}}

<body>
  <!-- Fixed navbar -->
  <nav class="navbar navbar-inverse navbar-fixed-top navbar-custom">
    <div class="container-fluid">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar"
          aria-expanded="false" aria-controls="navbar">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" href="#">
          <img class="brand-img" src="/public/img/brand-logo.png" alt="ERV-logo">
        </a>
      </div>
      <div id="navbar" class="navbar-collapse collapse">
        {{if .Username}}
        <ul class="nav navbar-nav navbar-right">
          <li class="navbar-text text-white">
            Innlogget som: <strong>{{.Username}}</strong>
            <a href="/auth/logout" class="logout-btn text-white" style="margin-left: 10px;">
              <i class="glyphicon glyphicon-log-out"></i> Logg ut
            </a>
          </li>
        </ul>
        {{end}}
      </div>
    </div>
  </nav>

  <!-- Begin page content -->
  <div class="container">
    <!-- Begin top warning banners-->
    <div class="row">
      <div class="col-12">
        <div id="successAlert" name="successAlert" class="alert alert-success alert-dismissible" role="alert"
          style="display:none">
          <button type="button" class="close" onclick="$('#successAlert').hide()" aria-label="Lukk"><span
              aria-hidden="true">&times;</span></button>
          <strong>OK!</strong>
          <div id="successAlertTxt" name="successAlertTxt"></div>
        </div>
        <div id="failedAlert" name="failedAlert" class="alert alert-danger alert-dismissible" role="alert"
          style="display:none">
          <button type="button" class="close" onclick="$('#failedAlert').hide()" aria-label="Lukk"><span
              aria-hidden="true">&times;</span></button>
          <strong>Feil!</strong>
          <div id="failedAlertTxt" name="failedAlertTxt"></div>
        </div>
      </div>
    </div>
    <!-- Begin page heading-->
    <div class="row">
      <div class="col">
        <div class="mt-3">
          <h1>Visma Business exporter</h1>
        </div>
        <p class="lead">Verktøy for å eksportere arbeidstid ifra Visma Business til et format som kan importeres i Visma
          Lønn.</p>
      </div>
    </div>
    <!-- Begin export-form -->
    <form role="form" class="form-horizontal" id="export">
      <div class="row">
        <div class="form-group col-md-6">
          <label for="database">Valgt database / Firma</label>
          <div class="input-group" id="database">
            <select class="form-control form-control-sm" id="database" name="database" disabled>
              <option value="{{ .Database}}">{{ .CompanyName}} ({{ .Database}})</option>
            </select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-4">
          <label for="exportFormat">Eksportformat</label>
          <div class="input-group" id="exportFormatGroup">
            <select class="form-control form-control-sm" id="exportFormat" name="exportFormat" disabled>
              <option value="VLSTANDARD" selected>Visma Lønn Standard</option>
            </select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-4">
          <label for="fromDate">Dato fra</label>
          <div class="input-group date" id="fromDate">
            <input type="text" class="form-control" name="fromDate" required />
            <span class="input-group-addon">
              <span class="glyphicon glyphicon-calendar"></span>
            </span>
          </div>
        </div>
        <div class="form-group col-md-4">
          <label for="toDate">Dato til</label>
          <div class="input-group date" id="toDate">
            <input type="text" class="form-control" name="toDate" required />
            <span class="input-group-addon">
              <span class="glyphicon glyphicon-calendar"></span>
            </span>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-4">
          <div class="input-group">
            <input class="form-check-input" type="checkbox" id="showExported" name="showExported" checked>
            <label class="form-check-label" for="showExported">Ta med overført</label>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-4">
          <div class="input-group">
            <input class="form-check-input" type="checkbox" id="showHourSalaries" name="showHourSalaries" checked>
            <label class="form-check-label" for="showHourSalaries">Ta med timelønn</label>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-4">
          <div class="input-group">
            <input class="form-check-input" type="checkbox" id="showMonthlySalaries" name="showMonthlySalaries" checked>
            <label class="form-check-label" for="showMonthlySalaries">Ta med månedslønn</label>
          </div>
        </div>
      </div>
      <div class="row">
        <label for="employees">Ansatte</label>
        <div class="input-group employees" id="employees" style="min-width: 300px;">
          <select style="width: 100%;" multiple="multiple" class="multipleEmployees" name="employees"
            id="employees"></select>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-lg-8">
          <label for="salaries">Lønnsart (månedslønnede)</label>
          <div class="input-group salaries" id="salaries" style="min-width: 300px;">
            <select style="width: 100%;" multiple="multiple" class="multipleSalaries" name="salaries"
              id="inputSalaries"></select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-lg-8">
          <label for="excludedSalaries">Ekskluder lønnsarter (månedslønnede)</label>
          <div class="input-group excludedSalaries" id="excludedSalaries" style="min-width: 300px;">
            <select style="width: 100%;" multiple="multiple" class="multipleSalaries" name="excludedSalaries"
              id="inputExcludedSalaries"></select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-4">
          <button type="submit" class="btn btn-primary" name="action" value="saveSelections" id="saveSelections">Lagre
            utvalg</button>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-2">
          <button type="submit" class="btn btn-primary" name="action" value="report"
            id="showExportReport">Forhåndsvis</button>
        </div>
        <div class="form-group col-md-2">
          <button type="submit" class="btn btn-info" name="action" value="download" id="downloadExport">Last
            ned</button>
        </div>
      </div>
    </form>
    <div class="row">
      <div id="form-result"></div>
    </div>
  </div>

  <!-- Begin footer-->
  <footer class="navbar navbar-fixed-bottom footer">
    <div class="row">
      <div class="container">
        <div class="col col-xs-12 text-center"><span class="text-muted">Visma Lønn exporter - {{ .Version }}</span>
        </div>
      </div>
    </div>
  </footer>

  <!-- Report modal-->
  <div id="report" class="modal" tabindex="-1" role="dialog" aria-labelledby="reportmodalLabel" aria-hidden="true"
    style="display: none;">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
          <h3 id="reportmodalLabel">Rapport</h3>
        </div>
        <div class="modal-body"></div>
        <div class="modal-footer">
          <button class="btn btn-danger" data-dismiss="modal" aria-hidden="true">Lukk</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Full screen loading gif -->
  <div class="loading"></div>

  <!-- Bootstrap core JavaScript
    ================================================== -->
  <!-- Placed at the end of the document so the pages load faster -->
  <script src="/public/vendor/jquery/jquery-2.2.4.min.js"></script>
  <script src="/public/vendor/bootstrap/js/bootstrap.min.js"></script>

  <!-- IE10 viewport hack for Surface/desktop Windows 8 bug -->
  <!-- <script src="/public/js/ie10-viewport-bug-workaround.js"></script> -->

  <!-- Bootstrap datepicker -->
  <!-- <script src="/public/js/moment.min.js"></script> -->
  <script src="/public/js/moment-with-locales.min.js"></script>
  <script src="/public/js/bootstrap-datepicker.min.js"></script>
  <script src="/public/js/bootstrap-datepicker.no.min.js"></script>

  <!-- Select2 plugin -->
  <script src="/public/js/select2.min.js"></script>

  <!-- Tagify form-input -->
  <script src="/public/js/jQuery.tagify.js"></script>

  <!-- JQuery form-validator -->
  <script src="/public/js/jquery.validate.js"></script>

  <!-- Authentication handling -->
  <script src="/public/js/auth.js"></script>

  <!-- Settings management -->
  <script src="/public/js/settings.js"></script>

  <!-- Datepicker script -->
  <script type="text/javascript">
    $('#fromDate').datepicker({
      format: "dd-mm-yyyy",
      weekStart: 1,
      todayBtn: "linked",
      language: "no",
      autoclose: true,
      todayHighlight: true,
      orientation: "bottom auto"
    });
    $('#toDate').datepicker({
      format: "dd-mm-yyyy",
      weekStart: 1,
      todayBtn: "linked",
      language: "no",
      autoclose: true,
      todayHighlight: true,
      orientation: "bottom auto"
    });
  </script>

  <!-- Form submission functions-->
  <script type="text/javascript">
    $(document).ready(function () {
      // Initialize select2 for employees
      $('.multipleEmployees').select2({
        placeholder: "Søk etter ansatt..",
        ajax: {
          url: '/employees',
          cache: true,
          dataType: 'json',
          debug: true
        }
      });

      // Initialize select2 for salary fields
      $('.multipleSalaries').select2({
        tags: true,
        tokenSeparators: [','],
        placeholder: "Skriv inn..."
      });

      // Load initial data for select2
      $.ajax({
        url: "/employees",
        type: "GET",
        async: false,
        error: function () {
          $('.loading').hide();
          $("#failedAlert").show();
          $("#failedAlertTxt").text("Problemer med å hente ansatte");
          window.setTimeout(function () {
            $("#failedAlert").hide();
          }, 15000);
        },
        success: function (response) {
          $('.loading').hide();
          if (response.results) {
            const select = $('.multipleEmployees');
            response.results.forEach(item => {
              const option = new Option(item.text, item.id, false, false);
              select.append(option);
            });
          }
        }
      });

      // Load saved form data from server
      if (window.settingsManager) {
        window.settingsManager.getFormData().then(data => {
          if (data) {
            $('input[name=toDate]').val(data.toDate || '');
            $('input[name=fromDate]').val(data.fromDate || '');
            $("#showExported").prop('checked', data.showExported || false);
            $("#showHourSalaries").prop('checked', data.showHourSalaries || false);
            $("#showMonthlySalaries").prop('checked', data.showMonthlySalaries || false);

            // Handle salary fields (using Select2)
            if (data.salaries && data.salaries.length > 0) {
              const salaryTags = data.salaries.map(id => ({
                id: id.trim(),
                text: id.trim()
              }));
              salaryTags.forEach(tag => {
                const option = new Option(tag.text, tag.id, true, true);
                $('#inputSalaries').append(option);
              });
              $('#inputSalaries').trigger('change');
            }

            if (data.excludedSalaries && data.excludedSalaries.length > 0) {
              const excludedTags = data.excludedSalaries.map(id => ({
                id: id.trim(),
                text: id.trim()
              }));
              excludedTags.forEach(tag => {
                const option = new Option(tag.text, tag.id, true, true);
                $('#inputExcludedSalaries').append(option);
              });
              $('#inputExcludedSalaries').trigger('change');
            }

            // Handle employees separately since it uses select2
            if (data.employees && data.employees.length > 0) {
              $('.multipleEmployees').val(data.employees);
              $('.multipleEmployees').trigger('change');
            }

            if (data.exportFormat) {
              $('#exportFormat').val(data.exportFormat);
            }
          }
        }).catch(error => {
          console.error('Failed to load form data:', error);
        });
      }
    });

    $('#showExportReport').click(function (e) {
      // prevent form submission
      e.preventDefault();

      var showExport = "off";
      if ($("#showExported").is(':checked'))
        showExport = "on";

      let showHourSalaries = "off";
      if ($("#showHourSalaries").is(':checked'))
        showHourSalaries = "on";

      let showMonthlySalaries = "off";
      if ($("#showMonthlySalaries").is(':checked'))
        showMonthlySalaries = "on";

      let selectedEmployees = $('.multipleEmployees').select2('data');
      let employees = selectedEmployees.map(e => e.id).join(",");

      let selectedSalaries = $('#inputSalaries').select2('data');
      let salaries = selectedSalaries.map(s => s.id).join(",");

      let selectedExcludedSalaries = $('#inputExcludedSalaries').select2('data');
      let excludedSalaries = selectedExcludedSalaries.map(s => s.id).join(",");

      var formData = {
        'toDate': $('input[name=toDate]').val(),
        'fromDate': $('input[name=fromDate]').val(),
        'showExported': showExport,
        'showHourSalaries': showHourSalaries,
        'showMonthlySalaries': showMonthlySalaries,
        'employees': employees,
        'salaries': salaries,
        'excludedSalaries': excludedSalaries
      };

      var formStatus = $('#export').validate().form();
      if (true == formStatus) {
        $('.loading').show();

        // submit the form via Ajax
        $.ajax({
          url: "/report",
          type: "POST",
          dataType: 'html',
          data: formData,
          error: function () {
            $('.loading').hide();
            $("#failedAlert").show();
            $("#failedAlertTxt").text("Problemer med å hente data til forhåndsvisning av rapporten.");
            window.setTimeout(function () {
              $("#failedAlert").hide();
            }, 15000);
          },
          success: function (response) {
            $('.loading').hide();
            $('.modal-body').html(response);
            $('#report').modal('toggle');
          }
        });
        $('.modal').modal('hide');
        $('.modal-backdrop').fadeOut(150);
      }
    });

    $('#submitExportForm').click(function (e) {
      // prevent form submission
      e.preventDefault();

      var showExport = "off";
      if ($("#showExported").is(':checked'))
        showExport = "on";

      let showHourSalaries = "off";
      if ($("#showHourSalaries").is(':checked'))
        showHourSalaries = "on";

      let showMonthlySalaries = "off";
      if ($("#showMonthlySalaries").is(':checked'))
        showMonthlySalaries = "on";

      let selectedEmployees = $('.multipleEmployees').select2('data');
      let employees = selectedEmployees.map(e => e.id).join(",");

      let selectedSalaries = $('#inputSalaries').select2('data');
      let salaries = selectedSalaries.map(s => s.id).join(",");

      let selectedExcludedSalaries = $('#inputExcludedSalaries').select2('data');
      let excludedSalaries = selectedExcludedSalaries.map(s => s.id).join(",");

      var formData = {
        'toDate': $('input[name=toDate]').val(),
        'fromDate': $('input[name=fromDate]').val(),
        'showExported': showExport,
        'showHourSalaries': showHourSalaries,
        'showMonthlySalaries': showMonthlySalaries,
        'employees': employees,
        'salaries': salaries,
        'excludedSalaries': excludedSalaries
      };

      var formStatus = $('#export').validate().form();
      if (true == formStatus) {
        $('.loading').show();

        // submit the form via Ajax
        $.ajax({
          url: "/export",
          type: "POST",
          dataType: 'html',
          data: formData,
          error: function () {
            $('.loading').hide();
            $("#failedAlert").show();
            $("#failedAlertTxt").text("Problemer med å hente data til rapporten.");
            window.setTimeout(function () {
              $("#failedAlert").hide();
            }, 15000);
          },
          success: function (response) {
            $('.loading').hide();
            $("#successAlert").show();
            $("#successAlertTxt").text("Rapporten ble generert uten problemer.");
            window.setTimeout(function () {
              $("#successAlert").hide();
            }, 5000);
          }
        });
        $('.modal').modal('hide');
        $('.modal-backdrop').fadeOut(150);
      }
    });

    $('#saveSelections').click(function (e) {
      // prevent form submission
      e.preventDefault();

      $('.loading').show();

      var showExport = "off";
      if ($("#showExported").is(':checked'))
        showExport = "on";

      let showHourSalaries = "off";
      if ($("#showHourSalaries").is(':checked'))
        showHourSalaries = "on";

      let showMonthlySalaries = "off";
      if ($("#showMonthlySalaries").is(':checked'))
        showMonthlySalaries = "on";

      let selectedEmployees = $('.multipleEmployees').select2('data');
      let employees = selectedEmployees.map(e => e.id).join(",");

      let selectedSalaries = $('#inputSalaries').select2('data');
      let salaries = selectedSalaries.map(s => s.id).join(",");

      let selectedExcludedSalaries = $('#inputExcludedSalaries').select2('data');
      let excludedSalaries = selectedExcludedSalaries.map(s => s.id).join(",");

      // Save to server using settings manager
      if (window.settingsManager) {
        window.settingsManager.saveCurrentFormData().then(success => {
          $('.loading').hide();
          if (success) {
            $("#successAlert").show();
            $("#successAlertTxt").text("Utvalget ble lagret uten problemer.");
            window.setTimeout(function () {
              $("#successAlert").hide();
            }, 5000);
          } else {
            $("#failedAlert").show();
            $("#failedAlertTxt").text("Problemer med å lagre utvalget.");
            window.setTimeout(function () {
              $("#failedAlert").hide();
            }, 5000);
          }
        }).catch(error => {
          $('.loading').hide();
          $("#failedAlert").show();
          $("#failedAlertTxt").text("Problemer med å lagre utvalget: " + error.message);
          window.setTimeout(function () {
            $("#failedAlert").hide();
          }, 5000);
        });
      } else {
        $('.loading').hide();
        $("#failedAlert").show();
        $("#failedAlertTxt").text("Settings manager ikke tilgjengelig.");
        window.setTimeout(function () {
          $("#failedAlert").hide();
        }, 5000);
      }

      $('.modal').modal('hide');
      $('.modal-backdrop').fadeOut(150);
    });

    $('#downloadExport').click(function (e) {
      // prevent form submission
      e.preventDefault();

      var showExport = "off";
      if ($("#showExported").is(':checked'))
        showExport = "on";

      let showHourSalaries = "off";
      if ($("#showHourSalaries").is(':checked'))
        showHourSalaries = "on";

      let showMonthlySalaries = "off";
      if ($("#showMonthlySalaries").is(':checked'))
        showMonthlySalaries = "on";

      let selectedEmployees = $('.multipleEmployees').select2('data');
      let employees = selectedEmployees.map(e => e.id).join(",");

      let selectedSalaries = $('#inputSalaries').select2('data');
      let salaries = selectedSalaries.map(s => s.id).join(",");

      let selectedExcludedSalaries = $('#inputExcludedSalaries').select2('data');
      let excludedSalaries = selectedExcludedSalaries.map(s => s.id).join(",");

      var formData = {
        'toDate': $('input[name=toDate]').val(),
        'fromDate': $('input[name=fromDate]').val(),
        'showExported': showExport,
        'showHourSalaries': showHourSalaries,
        'showMonthlySalaries': showMonthlySalaries,
        'employees': employees,
        'salaries': salaries,
        'excludedSalaries': excludedSalaries,
        'exportFormat': $('#exportFormat').val()
      };

      var formStatus = $('#export').validate().form();
      if (true == formStatus) {
        $('.loading').show();

        // Create a form and submit it to trigger file download
        var downloadForm = $('<form>', {
          'method': 'POST',
          'action': '/download',
          'target': '_blank'
        });

        // Add form data as hidden inputs
        $.each(formData, function (key, value) {
          $('<input>').attr({
            type: 'hidden',
            name: key,
            value: value
          }).appendTo(downloadForm);
        });

        // Add the form to the body and submit it
        downloadForm.appendTo('body').submit();
        downloadForm.remove();

        $('.loading').hide();
        $("#successAlert").show();
        $("#successAlertTxt").text("Nedlasting startet.");
        window.setTimeout(function () {
          $("#successAlert").hide();
        }, 5000);

        $('.modal').modal('hide');
        $('.modal-backdrop').fadeOut(150);
      }
    });
  </script>
</body>
{{end}}