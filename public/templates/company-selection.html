{{define "title"}}
ERV Teknikk AS - Velg firma
{{end}}

{{define "body"}}

<style>
  /* Custom styles for company selection page */
  html,
  body {
    height: 100%;
  }

  .company-selection-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .main-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 70px 20px;
    /* Account for fixed navbar and footer */
  }

  .company-card {
    width: 100%;
    max-width: 800px;
    min-width: 400px;
  }

  .list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    transition: all 0.2s ease-in-out;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .company-logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
  }

  /* Responsive adjustments */
  @media (max-width: 576px) {
    .main-content {
      padding: 80px 15px;
    }

    .company-card {
      min-width: 300px;
      max-width: 100%;
    }

    .list-group-item {
      padding: 15px;
    }
  }

  @media (min-width: 577px) and (max-width: 768px) {
    .main-content {
      padding: 70px 30px;
    }

    .company-card {
      max-width: 600px;
    }
  }

  @media (min-width: 769px) {
    .main-content {
      padding: 70px 40px;
    }

    .company-card {
      max-width: 700px;
    }
  }
</style>

<body class="company-selection-container">
  <!-- Fixed navbar -->
  <nav class="navbar navbar-inverse navbar-fixed-top navbar-custom">
    <div class="container-fluid">
      <div class="navbar-header">
        <a class="navbar-brand" href="#">
          <img class="brand-img" src="/public/img/brand-logo.png" alt="ERV-logo">
        </a>
      </div>
    </div>
  </nav>

  <!-- Main content area -->
  <div class="main-content">
    <div class="company-card">
      <div class="card">
        <div class="card-header text-center">
          <h3 class="mb-0">Logg inn</h3>
        </div>
        <div class="card-body">
          <p class="text-center mb-4">
            Skriv inn din e-postadresse for å logge inn til Visma Lønn exporter.
          </p>

          <form id="emailLoginForm" action="/auth/login" method="POST">
            <div class="form-group">
              <label for="email" class="sr-only">E-postadresse</label>
              <div class="input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text">
                    <i class="fa fa-envelope" aria-hidden="true"></i>
                  </span>
                </div>
                <input type="email" class="form-control form-control-lg" id="email" name="email"
                  placeholder="<EMAIL>" required autocomplete="email">
              </div>
              <small class="form-text text-muted">
                Vi bruker din e-postadresse for å finne riktig innloggingssystem.
              </small>
            </div>

            <div class="form-group text-center">
              <button type="submit" class="btn btn-primary btn-lg btn-block">
                <i class="fa fa-sign-in" aria-hidden="true"></i>
                Fortsett til innlogging
              </button>
            </div>
          </form>

          <div id="loadingSpinner" class="text-center d-none">
            <div class="spinner-border text-primary" role="status">
              <span class="sr-only">Behandler...</span>
            </div>
            <p class="mt-2">Finner innloggingssystem...</p>
          </div>

          <div id="errorMessage" class="alert alert-danger d-none">
            <h5>Feil ved innlogging</h5>
            <p id="errorText">Det oppstod en feil. Prøv igjen.</p>
          </div>

          <div class="text-center mt-4">
            <small class="text-muted">
              Problemer med innlogging? Kontakt systemadministrator for hjelp.
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="navbar navbar-fixed-bottom footer">
    <div class="container-fluid">
      <div class="row">
        <div class="col-xs-12 text-center">
          <span class="text-muted">Visma Lønn exporter - {{ .Version }}</span>
        </div>
      </div>
    </div>
  </footer>

  <!-- Bootstrap core JavaScript -->
  <script src="/public/vendor/jquery/jquery-2.2.4.min.js"></script>
  <script src="/public/vendor/bootstrap/js/bootstrap.min.js"></script>

  <!-- Email login JavaScript -->
  <script>
    $(document).ready(function () {
      $('#emailLoginForm').on('submit', function (e) {
        // Show loading spinner
        $('#loadingSpinner').removeClass('d-none');
        $('#errorMessage').addClass('d-none');

        // Disable form
        $('#email').prop('disabled', true);
        $('button[type="submit"]').prop('disabled', true);

        // Let the form submit normally
        // The server will handle the redirect to Keycloak
      });

      // Handle form validation
      $('#email').on('input', function () {
        var email = $(this).val();
        var isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

        if (email && !isValid) {
          $(this).addClass('is-invalid');
        } else {
          $(this).removeClass('is-invalid');
        }
      });

      // Focus on email input
      $('#email').focus();
    });
  </script>
</body>
{{end}}