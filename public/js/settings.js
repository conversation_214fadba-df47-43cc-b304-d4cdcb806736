/**
 * Settings management for ERV Lønn Exporter
 * Handles server-side storage of user settings and form data
 */

class SettingsManager {
    constructor() {
        this.baseUrl = '/api/settings';
    }

    /**
     * Get form data from server
     */
    async getFormData() {
        try {
            const response = await fetch(`${this.baseUrl}/form-data`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Retrieved form data from server:', data);
            return data;
        } catch (error) {
            console.error('Failed to get form data:', error);
            // Return default values if server request fails
            return {
                toDate: '',
                fromDate: '',
                showExported: false,
                showHourSalaries: false,
                showMonthlySalaries: false,
                employees: [],
                salaries: [],
                excludedSalaries: [],
                exportFormat: 'VLSTANDARD'
            };
        }
    }

    /**
     * Save form data to server
     */
    async saveFormData(formData) {
        try {
            const response = await fetch(`${this.baseUrl}/form-data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Saved form data to server:', result);
            return result;
        } catch (error) {
            console.error('Failed to save form data:', error);
            throw error;
        }
    }

    /**
     * Delete form data from server
     */
    async deleteFormData() {
        try {
            const response = await fetch(`${this.baseUrl}/form-data`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Deleted form data from server:', result);
            return result;
        } catch (error) {
            console.error('Failed to delete form data:', error);
            throw error;
        }
    }

    /**
     * Get a specific user setting
     */
    async getUserSetting(key) {
        try {
            const response = await fetch(`${this.baseUrl}/${encodeURIComponent(key)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data.value;
        } catch (error) {
            console.error(`Failed to get user setting '${key}':`, error);
            return null;
        }
    }

    /**
     * Set a specific user setting
     */
    async setUserSetting(key, value) {
        try {
            const response = await fetch(`${this.baseUrl}/${encodeURIComponent(key)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({ value: value })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log(`Set user setting '${key}':`, result);
            return result;
        } catch (error) {
            console.error(`Failed to set user setting '${key}':`, error);
            throw error;
        }
    }

    /**
     * Delete a specific user setting
     */
    async deleteUserSetting(key) {
        try {
            const response = await fetch(`${this.baseUrl}/${encodeURIComponent(key)}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log(`Deleted user setting '${key}':`, result);
            return result;
        } catch (error) {
            console.error(`Failed to delete user setting '${key}':`, error);
            throw error;
        }
    }

    /**
     * Load form data and populate the form
     */
    async loadFormData() {
        try {
            const formData = await this.getFormData();

            // Populate form fields
            if (formData.toDate) {
                $('input[name=toDate]').val(formData.toDate);
            }
            if (formData.fromDate) {
                $('input[name=fromDate]').val(formData.fromDate);
            }

            $("#showExported").prop('checked', formData.showExported || false);
            $("#showHourSalaries").prop('checked', formData.showHourSalaries || false);
            $("#showMonthlySalaries").prop('checked', formData.showMonthlySalaries || false);

            // Handle select2 fields if they exist
            if (formData.employees && formData.employees.length > 0) {
                // This would need to be implemented based on how the employees select2 is populated
                console.log('Employees to restore:', formData.employees);
            }

            if (formData.salaries && formData.salaries.length > 0) {
                console.log('Salaries to restore:', formData.salaries);
            }

            if (formData.excludedSalaries && formData.excludedSalaries.length > 0) {
                console.log('Excluded salaries to restore:', formData.excludedSalaries);
            }

            if (formData.exportFormat) {
                $('#exportFormat').val(formData.exportFormat);
            }

            console.log('Form data loaded successfully');
        } catch (error) {
            console.error('Failed to load form data:', error);
        }
    }

    /**
     * Collect current form data and save it
     */
    async saveCurrentFormData() {
        try {
            // Collect form data
            const showExported = $("#showExported").is(':checked');
            const showHourSalaries = $("#showHourSalaries").is(':checked');
            const showMonthlySalaries = $("#showMonthlySalaries").is(':checked');

            let employees = [];
            let salaries = [];
            let excludedSalaries = [];

            // Get select2 data if available
            if ($('.multipleEmployees').length > 0) {
                const selectedEmployees = $('.multipleEmployees').select2('data');
                employees = selectedEmployees.map(e => e.id);
            }

            if ($('#inputSalaries').length > 0) {
                const selectedSalaries = $('#inputSalaries').select2('data');
                salaries = selectedSalaries.map(s => s.id);
            }

            if ($('#inputExcludedSalaries').length > 0) {
                const selectedExcludedSalaries = $('#inputExcludedSalaries').select2('data');
                excludedSalaries = selectedExcludedSalaries.map(s => s.id);
            }

            const formData = {
                toDate: $('input[name=toDate]').val() || '',
                fromDate: $('input[name=fromDate]').val() || '',
                showExported: showExported,
                showHourSalaries: showHourSalaries,
                showMonthlySalaries: showMonthlySalaries,
                employees: employees,
                salaries: salaries,
                excludedSalaries: excludedSalaries,
                exportFormat: $('#exportFormat').val() || 'VLSTANDARD'
            };

            await this.saveFormData(formData);
            return true;
        } catch (error) {
            console.error('Failed to save current form data:', error);
            return false;
        }
    }
}

// Create global instance
window.settingsManager = new SettingsManager();

// Initialize when document is ready
$(document).ready(function () {
    // Load form data when page loads
    if (window.settingsManager) {
        window.settingsManager.loadFormData();
    }
});
