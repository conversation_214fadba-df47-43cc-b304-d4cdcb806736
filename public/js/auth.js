/**
 * Authentication handling for ERV Lønn Exporter
 */

$(document).ready(function () {
    // Check authentication status on page load
    checkAuthStatus();

    // Handle logout button clicks
    $('.logout-btn').on('click', function (e) {
        e.preventDefault();
        logout();
    });
});

/**
 * Check if user is authenticated
 */
function checkAuthStatus() {
    // This function can be extended to check token expiration
    // and refresh tokens if needed

    // For now, we rely on server-side session management
    // The server will redirect to login if session is invalid
}

/**
 * Handle logout
 */
function logout() {
    // Clear any client-side data (form data is now stored server-side)
    // No need to clear localStorage since we're using server-side storage

    // Redirect to logout endpoint
    window.location.href = '/auth/logout';
}

/**
 * Handle authentication errors
 */
function handleAuthError(xhr, status, error) {
    if (xhr.status === 401) {
        // Unauthorized - redirect to login
        window.location.href = '/login';
    } else if (xhr.status === 403) {
        // Forbidden - show error message
        showAlert('error', 'Du har ikke tilgang til denne funksjonen. Kontakt systemadministrator.');
    } else {
        // Other errors
        showAlert('error', 'En feil oppstod: ' + error);
    }
}

/**
 * Show alert message
 */
function showAlert(type, message) {
    var alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    var alertId = type === 'error' ? 'failedAlert' : 'successAlert';
    var alertTextId = type === 'error' ? 'failedAlertTxt' : 'successAlertTxt';

    $('#' + alertTextId).text(message);
    $('#' + alertId).show();

    // Auto-hide after 5 seconds
    setTimeout(function () {
        $('#' + alertId).hide();
    }, 5000);
}

/**
 * Enhanced AJAX wrapper with authentication error handling
 */
function authenticatedAjax(options) {
    var originalError = options.error;

    options.error = function (xhr, status, error) {
        handleAuthError(xhr, status, error);

        // Call original error handler if provided
        if (originalError) {
            originalError(xhr, status, error);
        }
    };

    return $.ajax(options);
}

// Override the global AJAX error handler for authentication
$(document).ajaxError(function (event, xhr, settings, thrownError) {
    if (xhr.status === 401) {
        // Unauthorized - redirect to login
        window.location.href = '/login';
    }
});
