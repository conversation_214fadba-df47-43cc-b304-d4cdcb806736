package app

import (
	"fmt"

	"github.com/dyuri/go-figure"
)

var (
	Title        = fmt.Sprintf("\n%s", figure.NewFigure(ApplicationName, "3d", true).String())
	Version      string
	Branch       string
	Commit       string
	Build        string
	GoVersion    string
	SummaryTitle = Title + "\n>>>Version  : " + Version + "\n>>>Author   : " + Author + "\n>>>HomePage : " + HomePage
	ShortTitle   = ApplicationName + "/" + Version + " (" + Branch + "@" + Commit + ")\n↳ " + GoVersion + " --> " + Build
)
