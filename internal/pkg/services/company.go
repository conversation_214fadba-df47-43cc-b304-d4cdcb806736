package services

import (
	"fmt"
	"log/slog"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
)

// CompanyService handles company operations
type CompanyService struct {
	repo database.Repository
}

// NewCompanyService creates a new company service
func NewCompanyService(repo database.Repository) *CompanyService {
	return &CompanyService{
		repo: repo,
	}
}

// GetCompanies retrieves all companies
func (s *CompanyService) GetCompanies() ([]*database.Company, error) {
	companies, err := s.repo.GetCompanies()
	if err != nil {
		return nil, fmt.Errorf("failed to get companies: %w", err)
	}

	return companies, nil
}

// GetCompany retrieves a company by ID
func (s *CompanyService) GetCompany(id int) (*database.Company, error) {
	if id <= 0 {
		return nil, fmt.Errorf("invalid company ID")
	}

	company, err := s.repo.GetCompany(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get company: %w", err)
	}

	return company, nil
}

// GetCompanyByName retrieves a company by name
func (s *CompanyService) GetCompanyByName(name string) (*database.Company, error) {
	if name == "" {
		return nil, fmt.Errorf("company name is required")
	}

	company, err := s.repo.GetCompanyByName(name)
	if err != nil {
		return nil, fmt.Errorf("failed to get company by name: %w", err)
	}

	return company, nil
}

// CreateCompany creates a new company
func (s *CompanyService) CreateCompany(company *database.Company) error {
	if company == nil {
		return fmt.Errorf("company is required")
	}

	if company.Name == "" || company.DisplayName == "" {
		return fmt.Errorf("company name and display name are required")
	}

	if company.KeycloakURL == "" || company.KeycloakRealm == "" || company.KeycloakClientID == "" {
		return fmt.Errorf("keycloak configuration is required")
	}

	if err := s.repo.CreateCompany(company); err != nil {
		return fmt.Errorf("failed to create company: %w", err)
	}

	slog.Info("Created company", "id", company.ID, "name", company.Name)
	return nil
}

// UpdateCompany updates an existing company
func (s *CompanyService) UpdateCompany(company *database.Company) error {
	if company == nil {
		return fmt.Errorf("company is required")
	}

	if company.ID <= 0 {
		return fmt.Errorf("invalid company ID")
	}

	if company.Name == "" || company.DisplayName == "" {
		return fmt.Errorf("company name and display name are required")
	}

	if company.KeycloakURL == "" || company.KeycloakRealm == "" || company.KeycloakClientID == "" {
		return fmt.Errorf("keycloak configuration is required")
	}

	if err := s.repo.UpdateCompany(company); err != nil {
		return fmt.Errorf("failed to update company: %w", err)
	}

	slog.Info("Updated company", "id", company.ID, "name", company.Name)
	return nil
}

// DeleteCompany deletes a company
func (s *CompanyService) DeleteCompany(id int) error {
	if id <= 0 {
		return fmt.Errorf("invalid company ID")
	}

	if err := s.repo.DeleteCompany(id); err != nil {
		return fmt.Errorf("failed to delete company: %w", err)
	}

	slog.Info("Deleted company", "id", id)
	return nil
}

// DatabaseConnectionService handles database connection operations
type DatabaseConnectionService struct {
	repo database.Repository
}

// NewDatabaseConnectionService creates a new database connection service
func NewDatabaseConnectionService(repo database.Repository) *DatabaseConnectionService {
	return &DatabaseConnectionService{
		repo: repo,
	}
}

// GetDatabaseConnectionByCompany retrieves the database connection for a company
func (s *DatabaseConnectionService) GetDatabaseConnectionByCompany(companyID int) (*database.DatabaseConnection, error) {
	if companyID <= 0 {
		return nil, fmt.Errorf("invalid company ID")
	}

	connection, err := s.repo.GetDatabaseConnectionByCompany(companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	return connection, nil
}

// GetDatabaseConnection retrieves a database connection by ID
func (s *DatabaseConnectionService) GetDatabaseConnection(id int) (*database.DatabaseConnection, error) {
	if id <= 0 {
		return nil, fmt.Errorf("invalid database connection ID")
	}

	connection, err := s.repo.GetDatabaseConnection(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	return connection, nil
}

// CreateDatabaseConnection creates a new database connection
func (s *DatabaseConnectionService) CreateDatabaseConnection(connection *database.DatabaseConnection) error {
	if connection == nil {
		return fmt.Errorf("database connection is required")
	}

	if connection.CompanyID <= 0 {
		return fmt.Errorf("invalid company ID")
	}

	if connection.Server == "" || connection.DatabaseName == "" {
		return fmt.Errorf("server and database name are required")
	}

	if connection.Params == nil {
		connection.Params = make(map[string]string)
	}

	if err := s.repo.CreateDatabaseConnection(connection); err != nil {
		return fmt.Errorf("failed to create database connection: %w", err)
	}

	slog.Info("Created database connection", "id", connection.ID, "company_id", connection.CompanyID)
	return nil
}

// UpdateDatabaseConnection updates an existing database connection
func (s *DatabaseConnectionService) UpdateDatabaseConnection(connection *database.DatabaseConnection) error {
	if connection == nil {
		return fmt.Errorf("database connection is required")
	}

	if connection.ID <= 0 {
		return fmt.Errorf("invalid database connection ID")
	}

	if connection.CompanyID <= 0 {
		return fmt.Errorf("invalid company ID")
	}

	if connection.Server == "" || connection.DatabaseName == "" {
		return fmt.Errorf("server and database name are required")
	}

	if connection.Params == nil {
		connection.Params = make(map[string]string)
	}

	if err := s.repo.UpdateDatabaseConnection(connection); err != nil {
		return fmt.Errorf("failed to update database connection: %w", err)
	}

	slog.Info("Updated database connection", "id", connection.ID, "company_id", connection.CompanyID)
	return nil
}

// DeleteDatabaseConnection deletes a database connection
func (s *DatabaseConnectionService) DeleteDatabaseConnection(id int) error {
	if id <= 0 {
		return fmt.Errorf("invalid database connection ID")
	}

	if err := s.repo.DeleteDatabaseConnection(id); err != nil {
		return fmt.Errorf("failed to delete database connection: %w", err)
	}

	slog.Info("Deleted database connection", "id", id)
	return nil
}

// UserSessionService handles user session operations
type UserSessionService struct {
	repo database.Repository
}

// NewUserSessionService creates a new user session service
func NewUserSessionService(repo database.Repository) *UserSessionService {
	return &UserSessionService{
		repo: repo,
	}
}

// GetUserSession retrieves a user session
func (s *UserSessionService) GetUserSession(username string) (*database.UserSession, error) {
	if username == "" {
		return nil, fmt.Errorf("username is required")
	}

	session, err := s.repo.GetUserSession(username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user session: %w", err)
	}

	return session, nil
}

// CreateOrUpdateUserSession creates or updates a user session
func (s *UserSessionService) CreateOrUpdateUserSession(session *database.UserSession) error {
	if session == nil {
		return fmt.Errorf("user session is required")
	}

	if session.Username == "" {
		return fmt.Errorf("username is required")
	}

	// Check if session already exists
	existingSession, err := s.repo.GetUserSession(session.Username)
	if err != nil {
		return fmt.Errorf("failed to check existing session: %w", err)
	}

	if existingSession != nil {
		// Update existing session
		session.ID = existingSession.ID
		if err := s.repo.UpdateUserSession(session); err != nil {
			return fmt.Errorf("failed to update user session: %w", err)
		}
		slog.Debug("Updated user session", "username", session.Username)
	} else {
		// Create new session
		if err := s.repo.CreateUserSession(session); err != nil {
			return fmt.Errorf("failed to create user session: %w", err)
		}
		slog.Debug("Created user session", "username", session.Username)
	}

	return nil
}

// UpdateCurrentCompany updates the user's current company context
func (s *UserSessionService) UpdateCurrentCompany(username string, companyID int) error {
	if username == "" {
		return fmt.Errorf("username is required")
	}

	if companyID <= 0 {
		return fmt.Errorf("invalid company ID")
	}

	session, err := s.repo.GetUserSession(username)
	if err != nil {
		return fmt.Errorf("failed to get user session: %w", err)
	}

	if session == nil {
		return fmt.Errorf("user session not found")
	}

	// Check if user has access to the company
	hasAccess := false
	for _, accessibleCompanyID := range session.AccessibleCompanies {
		if accessibleCompanyID == companyID {
			hasAccess = true
			break
		}
	}

	if !hasAccess {
		return fmt.Errorf("user does not have access to company %d", companyID)
	}

	// Update current company
	session.CurrentCompanyID = &companyID
	if err := s.repo.UpdateUserSession(session); err != nil {
		return fmt.Errorf("failed to update current company: %w", err)
	}

	slog.Info("Updated user's current company", "username", username, "company_id", companyID)
	return nil
}

// DeleteUserSession deletes a user session
func (s *UserSessionService) DeleteUserSession(username string) error {
	if username == "" {
		return fmt.Errorf("username is required")
	}

	if err := s.repo.DeleteUserSession(username); err != nil {
		return fmt.Errorf("failed to delete user session: %w", err)
	}

	slog.Info("Deleted user session", "username", username)
	return nil
}
