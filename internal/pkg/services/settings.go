package services

import (
	"fmt"
	"log/slog"
	"time"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
)

// SettingsService handles user settings and form data operations
type SettingsService struct {
	repo database.Repository
}

// NewSettingsService creates a new settings service
func NewSettingsService(repo database.Repository) *SettingsService {
	return &SettingsService{
		repo: repo,
	}
}

// GetFormData retrieves form data for a user
func (s *SettingsService) GetFormData(username string) (*database.FormDataRequest, error) {
	if username == "" {
		return nil, fmt.Errorf("username is required")
	}

	formData, err := s.repo.GetFormData(username)
	if err != nil {
		return nil, fmt.Errorf("failed to get form data: %w", err)
	}

	// If no form data exists, return default values
	if formData == nil {
		return &database.FormDataRequest{
			ShowExported:        false,
			ShowHourSalaries:    false,
			ShowMonthlySalaries: false,
			Employees:           []string{},
			Salaries:            []string{},
			ExcludedSalaries:    []string{},
			ExportFormat:        "VLSTANDARD",
		}, nil
	}

	// Convert to request format
	formDataRequest, err := formData.ToFormDataRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to convert form data: %w", err)
	}

	slog.Debug("Retrieved form data for user", "username", username)
	return formDataRequest, nil
}

// SaveFormData saves form data for a user
func (s *SettingsService) SaveFormData(username string, formDataRequest *database.FormDataRequest) error {
	if username == "" {
		return fmt.Errorf("username is required")
	}

	if formDataRequest == nil {
		return fmt.Errorf("form data is required")
	}

	// Convert to database model
	formData, err := formDataRequest.ToFormData(username)
	if err != nil {
		return fmt.Errorf("failed to convert form data: %w", err)
	}

	// Save to database
	if err := s.repo.SaveFormData(formData); err != nil {
		return fmt.Errorf("failed to save form data: %w", err)
	}

	slog.Debug("Saved form data for user", "username", username)
	return nil
}

// DeleteFormData deletes form data for a user
func (s *SettingsService) DeleteFormData(username string) error {
	if username == "" {
		return fmt.Errorf("username is required")
	}

	if err := s.repo.DeleteFormData(username); err != nil {
		return fmt.Errorf("failed to delete form data: %w", err)
	}

	slog.Debug("Deleted form data for user", "username", username)
	return nil
}

// GetUserSetting retrieves a specific user setting
func (s *SettingsService) GetUserSetting(username, key string) (string, error) {
	if username == "" || key == "" {
		return "", fmt.Errorf("username and key are required")
	}

	setting, err := s.repo.GetUserSetting(username, key)
	if err != nil {
		return "", fmt.Errorf("failed to get user setting: %w", err)
	}

	if setting == nil {
		return "", nil // Setting doesn't exist
	}

	return setting.SettingValue, nil
}

// SetUserSetting sets a user setting
func (s *SettingsService) SetUserSetting(username, key, value string) error {
	if username == "" || key == "" {
		return fmt.Errorf("username and key are required")
	}

	if err := s.repo.SetUserSetting(username, key, value); err != nil {
		return fmt.Errorf("failed to set user setting: %w", err)
	}

	slog.Debug("Set user setting", "username", username, "key", key)
	return nil
}

// DeleteUserSetting deletes a user setting
func (s *SettingsService) DeleteUserSetting(username, key string) error {
	if username == "" || key == "" {
		return fmt.Errorf("username and key are required")
	}

	if err := s.repo.DeleteUserSetting(username, key); err != nil {
		return fmt.Errorf("failed to delete user setting: %w", err)
	}

	slog.Debug("Deleted user setting", "username", username, "key", key)
	return nil
}

// SessionService handles session operations
type SessionService struct {
	repo database.Repository
}

// NewSessionService creates a new session service
func NewSessionService(repo database.Repository) *SessionService {
	return &SessionService{
		repo: repo,
	}
}

// GetSession retrieves a session by ID
func (s *SessionService) GetSession(sessionID string) (*database.Session, error) {
	if sessionID == "" {
		return nil, fmt.Errorf("session ID is required")
	}

	session, err := s.repo.GetSession(sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	return session, nil
}

// CreateSession creates a new session
func (s *SessionService) CreateSession(sessionID, username, accessToken, refreshToken string, expiresAt time.Time) error {
	if sessionID == "" || username == "" {
		return fmt.Errorf("session ID and username are required")
	}

	session := &database.Session{
		ID:           sessionID,
		Username:     username,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
	}

	if err := s.repo.CreateSession(session); err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}

	slog.Debug("Created session", "session_id", sessionID, "username", username)
	return nil
}

// UpdateSession updates an existing session
func (s *SessionService) UpdateSession(sessionID, accessToken, refreshToken string, expiresAt time.Time) error {
	if sessionID == "" {
		return fmt.Errorf("session ID is required")
	}

	// Get existing session to preserve username
	existingSession, err := s.repo.GetSession(sessionID)
	if err != nil {
		return fmt.Errorf("failed to get existing session: %w", err)
	}

	if existingSession == nil {
		return fmt.Errorf("session not found")
	}

	session := &database.Session{
		ID:           sessionID,
		Username:     existingSession.Username,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
	}

	if err := s.repo.UpdateSession(session); err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}

	slog.Debug("Updated session", "session_id", sessionID)
	return nil
}

// DeleteSession deletes a session
func (s *SessionService) DeleteSession(sessionID string) error {
	if sessionID == "" {
		return fmt.Errorf("session ID is required")
	}

	if err := s.repo.DeleteSession(sessionID); err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}

	slog.Debug("Deleted session", "session_id", sessionID)
	return nil
}

// DeleteUserSessions deletes all sessions for a user
func (s *SessionService) DeleteUserSessions(username string) error {
	if username == "" {
		return fmt.Errorf("username is required")
	}

	if err := s.repo.DeleteUserSessions(username); err != nil {
		return fmt.Errorf("failed to delete user sessions: %w", err)
	}

	slog.Debug("Deleted all sessions for user", "username", username)
	return nil
}

// CleanupExpiredSessions removes expired sessions
func (s *SessionService) CleanupExpiredSessions() error {
	if err := s.repo.CleanupExpiredSessions(); err != nil {
		return fmt.Errorf("failed to cleanup expired sessions: %w", err)
	}

	return nil
}
