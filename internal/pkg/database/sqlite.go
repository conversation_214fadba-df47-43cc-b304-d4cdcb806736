package database

import (
	"database/sql"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

// SQLiteConfig holds the configuration for SQLite database
type SQLiteConfig struct {
	DatabasePath string
	MaxOpenConns int
	MaxIdleConns int
}

// SQLiteDB wraps the SQLite database connection
type SQLiteDB struct {
	db     *sql.DB
	config *SQLiteConfig
}

// NewSQLiteDB creates a new SQLite database connection
func NewSQLiteDB(config *SQLiteConfig) (*SQLiteDB, error) {
	// Ensure the directory exists
	dir := filepath.Dir(config.DatabasePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// Open database connection
	db, err := sql.Open("sqlite3", config.DatabasePath+"?_foreign_keys=on&_journal_mode=WAL")
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)

	// Test connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	sqliteDB := &SQLiteDB{
		db:     db,
		config: config,
	}

	// Run migrations
	if err := sqliteDB.migrate(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to run migrations: %w", err)
	}

	slog.Info("SQLite database initialized", "path", config.DatabasePath)
	return sqliteDB, nil
}

// Close closes the database connection
func (s *SQLiteDB) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// DB returns the underlying sql.DB instance
func (s *SQLiteDB) DB() *sql.DB {
	return s.db
}

// migrate runs database migrations
func (s *SQLiteDB) migrate() error {
	migrations := []string{
		// Create user_settings table
		`CREATE TABLE IF NOT EXISTS user_settings (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username TEXT NOT NULL,
			setting_key TEXT NOT NULL,
			setting_value TEXT NOT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(username, setting_key)
		)`,

		// Create sessions table
		`CREATE TABLE IF NOT EXISTS sessions (
			id TEXT PRIMARY KEY,
			username TEXT NOT NULL,
			access_token TEXT,
			refresh_token TEXT,
			expires_at DATETIME NOT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,

		// Create companies table for storing company configurations
		`CREATE TABLE IF NOT EXISTS companies (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL UNIQUE,
			display_name TEXT NOT NULL,
			keycloak_url TEXT NOT NULL,
			keycloak_realm TEXT NOT NULL,
			keycloak_client_id TEXT NOT NULL,
			keycloak_client_secret TEXT NOT NULL,
			keycloak_required_role TEXT NOT NULL,
			enabled BOOLEAN DEFAULT TRUE,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,

		// Create database_connections table for storing Visma database configurations
		// Each company has exactly one database connection (1:1 relationship)
		`CREATE TABLE IF NOT EXISTS database_connections (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			company_id INTEGER NOT NULL UNIQUE, -- One connection per company
			server TEXT NOT NULL,
			instance TEXT,
			port INTEGER NOT NULL,
			username TEXT NOT NULL,
			password TEXT NOT NULL,
			database_name TEXT NOT NULL,
			params TEXT, -- JSON object for extra parameters
			enabled BOOLEAN DEFAULT TRUE,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE
		)`,

		// Create user_sessions table for storing current user context
		`CREATE TABLE IF NOT EXISTS user_sessions (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username TEXT NOT NULL,
			current_company_id INTEGER,
			accessible_companies TEXT, -- JSON array of company IDs user has access to
			session_data TEXT, -- JSON object for additional session data
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (current_company_id) REFERENCES companies (id) ON DELETE SET NULL,
			UNIQUE(username)
		)`,

		// Create form_data table for storing user's form preferences (updated with database_id)
		`CREATE TABLE IF NOT EXISTS form_data (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username TEXT NOT NULL,
			database_id INTEGER,
			to_date TEXT,
			from_date TEXT,
			show_exported BOOLEAN DEFAULT FALSE,
			show_hour_salaries BOOLEAN DEFAULT FALSE,
			show_monthly_salaries BOOLEAN DEFAULT FALSE,
			employees TEXT, -- JSON array of selected employee IDs
			salaries TEXT, -- JSON array of selected salary IDs
			excluded_salaries TEXT, -- JSON array of excluded salary IDs
			export_format TEXT DEFAULT 'VLSTANDARD',
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (database_id) REFERENCES database_connections (id) ON DELETE SET NULL,
			UNIQUE(username, database_id)
		)`,

		// Create indexes for better performance
		`CREATE INDEX IF NOT EXISTS idx_user_settings_username ON user_settings(username)`,
		`CREATE INDEX IF NOT EXISTS idx_sessions_username ON sessions(username)`,
		`CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at)`,
		`CREATE INDEX IF NOT EXISTS idx_form_data_username ON form_data(username)`,
	}

	for i, migration := range migrations {
		if _, err := s.db.Exec(migration); err != nil {
			return fmt.Errorf("failed to execute migration %d: %w", i+1, err)
		}
	}

	slog.Debug("Database migrations completed successfully")
	return nil
}

// CleanupExpiredSessions removes expired sessions from the database
func (s *SQLiteDB) CleanupExpiredSessions() error {
	query := `DELETE FROM sessions WHERE expires_at < datetime('now')`
	result, err := s.db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to cleanup expired sessions: %w", err)
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected > 0 {
		slog.Debug("Cleaned up expired sessions", "count", rowsAffected)
	}

	return nil
}
