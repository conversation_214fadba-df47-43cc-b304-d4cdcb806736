package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"
)

// UserSetting represents a user setting in the database
type UserSetting struct {
	ID           int       `json:"id"`
	Username     string    `json:"username"`
	Setting<PERSON>ey   string    `json:"setting_key"`
	SettingValue string    `json:"setting_value"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// Session represents a user session in the database
type Session struct {
	ID           string    `json:"id"`
	Username     string    `json:"username"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// FormData represents user's form preferences
type FormData struct {
	ID                   int       `json:"id"`
	Username             string    `json:"username"`
	ToDate               string    `json:"to_date"`
	FromDate             string    `json:"from_date"`
	ShowExported         bool      `json:"show_exported"`
	ShowHourSalaries     bool      `json:"show_hour_salaries"`
	ShowMonthlySalaries  bool      `json:"show_monthly_salaries"`
	Employees            string    `json:"employees"`            // JSON array
	Salaries             string    `json:"salaries"`             // JSON array
	ExcludedSalaries     string    `json:"excluded_salaries"`    // JSON array
	ExportFormat         string    `json:"export_format"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

// FormDataRequest represents the structure for form data API requests
type FormDataRequest struct {
	ToDate              string   `json:"toDate"`
	FromDate            string   `json:"fromDate"`
	ShowExported        bool     `json:"showExported"`
	ShowHourSalaries    bool     `json:"showHourSalaries"`
	ShowMonthlySalaries bool     `json:"showMonthlySalaries"`
	Employees           []string `json:"employees"`
	Salaries            []string `json:"salaries"`
	ExcludedSalaries    []string `json:"excludedSalaries"`
	ExportFormat        string   `json:"exportFormat"`
}

// ToFormData converts FormDataRequest to FormData model
func (fdr *FormDataRequest) ToFormData(username string) (*FormData, error) {
	employeesJSON, err := json.Marshal(fdr.Employees)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal employees: %w", err)
	}

	salariesJSON, err := json.Marshal(fdr.Salaries)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal salaries: %w", err)
	}

	excludedSalariesJSON, err := json.Marshal(fdr.ExcludedSalaries)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal excluded salaries: %w", err)
	}

	return &FormData{
		Username:            username,
		ToDate:              fdr.ToDate,
		FromDate:            fdr.FromDate,
		ShowExported:        fdr.ShowExported,
		ShowHourSalaries:    fdr.ShowHourSalaries,
		ShowMonthlySalaries: fdr.ShowMonthlySalaries,
		Employees:           string(employeesJSON),
		Salaries:            string(salariesJSON),
		ExcludedSalaries:    string(excludedSalariesJSON),
		ExportFormat:        fdr.ExportFormat,
	}, nil
}

// ToFormDataRequest converts FormData to FormDataRequest
func (fd *FormData) ToFormDataRequest() (*FormDataRequest, error) {
	var employees []string
	if fd.Employees != "" {
		if err := json.Unmarshal([]byte(fd.Employees), &employees); err != nil {
			return nil, fmt.Errorf("failed to unmarshal employees: %w", err)
		}
	}

	var salaries []string
	if fd.Salaries != "" {
		if err := json.Unmarshal([]byte(fd.Salaries), &salaries); err != nil {
			return nil, fmt.Errorf("failed to unmarshal salaries: %w", err)
		}
	}

	var excludedSalaries []string
	if fd.ExcludedSalaries != "" {
		if err := json.Unmarshal([]byte(fd.ExcludedSalaries), &excludedSalaries); err != nil {
			return nil, fmt.Errorf("failed to unmarshal excluded salaries: %w", err)
		}
	}

	return &FormDataRequest{
		ToDate:              fd.ToDate,
		FromDate:            fd.FromDate,
		ShowExported:        fd.ShowExported,
		ShowHourSalaries:    fd.ShowHourSalaries,
		ShowMonthlySalaries: fd.ShowMonthlySalaries,
		Employees:           employees,
		Salaries:            salaries,
		ExcludedSalaries:    excludedSalaries,
		ExportFormat:        fd.ExportFormat,
	}, nil
}

// Repository interface for database operations
type Repository interface {
	// User Settings
	GetUserSetting(username, key string) (*UserSetting, error)
	SetUserSetting(username, key, value string) error
	DeleteUserSetting(username, key string) error

	// Sessions
	GetSession(sessionID string) (*Session, error)
	CreateSession(session *Session) error
	UpdateSession(session *Session) error
	DeleteSession(sessionID string) error
	DeleteUserSessions(username string) error

	// Form Data
	GetFormData(username string) (*FormData, error)
	SaveFormData(formData *FormData) error
	DeleteFormData(username string) error

	// Cleanup
	CleanupExpiredSessions() error
}

// SQLiteRepository implements Repository interface for SQLite
type SQLiteRepository struct {
	db *SQLiteDB
}

// NewSQLiteRepository creates a new SQLite repository
func NewSQLiteRepository(db *SQLiteDB) Repository {
	return &SQLiteRepository{db: db}
}

// GetUserSetting retrieves a user setting
func (r *SQLiteRepository) GetUserSetting(username, key string) (*UserSetting, error) {
	query := `SELECT id, username, setting_key, setting_value, created_at, updated_at 
			  FROM user_settings WHERE username = ? AND setting_key = ?`
	
	var setting UserSetting
	err := r.db.DB().QueryRow(query, username, key).Scan(
		&setting.ID, &setting.Username, &setting.SettingKey, &setting.SettingValue,
		&setting.CreatedAt, &setting.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get user setting: %w", err)
	}
	
	return &setting, nil
}

// SetUserSetting creates or updates a user setting
func (r *SQLiteRepository) SetUserSetting(username, key, value string) error {
	query := `INSERT INTO user_settings (username, setting_key, setting_value, updated_at)
			  VALUES (?, ?, ?, datetime('now'))
			  ON CONFLICT(username, setting_key) DO UPDATE SET
			  setting_value = excluded.setting_value,
			  updated_at = datetime('now')`
	
	_, err := r.db.DB().Exec(query, username, key, value)
	if err != nil {
		return fmt.Errorf("failed to set user setting: %w", err)
	}
	
	return nil
}

// DeleteUserSetting deletes a user setting
func (r *SQLiteRepository) DeleteUserSetting(username, key string) error {
	query := `DELETE FROM user_settings WHERE username = ? AND setting_key = ?`
	_, err := r.db.DB().Exec(query, username, key)
	if err != nil {
		return fmt.Errorf("failed to delete user setting: %w", err)
	}
	return nil
}

// GetSession retrieves a session by ID
func (r *SQLiteRepository) GetSession(sessionID string) (*Session, error) {
	query := `SELECT id, username, access_token, refresh_token, expires_at, created_at, updated_at
			  FROM sessions WHERE id = ? AND expires_at > datetime('now')`
	
	var session Session
	err := r.db.DB().QueryRow(query, sessionID).Scan(
		&session.ID, &session.Username, &session.AccessToken, &session.RefreshToken,
		&session.ExpiresAt, &session.CreatedAt, &session.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}
	
	return &session, nil
}

// CreateSession creates a new session
func (r *SQLiteRepository) CreateSession(session *Session) error {
	query := `INSERT INTO sessions (id, username, access_token, refresh_token, expires_at)
			  VALUES (?, ?, ?, ?, ?)`
	
	_, err := r.db.DB().Exec(query, session.ID, session.Username, session.AccessToken,
		session.RefreshToken, session.ExpiresAt)
	if err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}
	
	return nil
}

// UpdateSession updates an existing session
func (r *SQLiteRepository) UpdateSession(session *Session) error {
	query := `UPDATE sessions SET access_token = ?, refresh_token = ?, expires_at = ?, 
			  updated_at = datetime('now') WHERE id = ?`
	
	_, err := r.db.DB().Exec(query, session.AccessToken, session.RefreshToken,
		session.ExpiresAt, session.ID)
	if err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}
	
	return nil
}

// DeleteSession deletes a session
func (r *SQLiteRepository) DeleteSession(sessionID string) error {
	query := `DELETE FROM sessions WHERE id = ?`
	_, err := r.db.DB().Exec(query, sessionID)
	if err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}
	return nil
}

// DeleteUserSessions deletes all sessions for a user
func (r *SQLiteRepository) DeleteUserSessions(username string) error {
	query := `DELETE FROM sessions WHERE username = ?`
	_, err := r.db.DB().Exec(query, username)
	if err != nil {
		return fmt.Errorf("failed to delete user sessions: %w", err)
	}
	return nil
}

// GetFormData retrieves form data for a user
func (r *SQLiteRepository) GetFormData(username string) (*FormData, error) {
	query := `SELECT id, username, to_date, from_date, show_exported, show_hour_salaries,
			  show_monthly_salaries, employees, salaries, excluded_salaries, export_format,
			  created_at, updated_at FROM form_data WHERE username = ?`
	
	var formData FormData
	err := r.db.DB().QueryRow(query, username).Scan(
		&formData.ID, &formData.Username, &formData.ToDate, &formData.FromDate,
		&formData.ShowExported, &formData.ShowHourSalaries, &formData.ShowMonthlySalaries,
		&formData.Employees, &formData.Salaries, &formData.ExcludedSalaries,
		&formData.ExportFormat, &formData.CreatedAt, &formData.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get form data: %w", err)
	}
	
	return &formData, nil
}

// SaveFormData creates or updates form data for a user
func (r *SQLiteRepository) SaveFormData(formData *FormData) error {
	query := `INSERT INTO form_data (username, to_date, from_date, show_exported, 
			  show_hour_salaries, show_monthly_salaries, employees, salaries, 
			  excluded_salaries, export_format, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
			  ON CONFLICT(username) DO UPDATE SET
			  to_date = excluded.to_date,
			  from_date = excluded.from_date,
			  show_exported = excluded.show_exported,
			  show_hour_salaries = excluded.show_hour_salaries,
			  show_monthly_salaries = excluded.show_monthly_salaries,
			  employees = excluded.employees,
			  salaries = excluded.salaries,
			  excluded_salaries = excluded.excluded_salaries,
			  export_format = excluded.export_format,
			  updated_at = datetime('now')`
	
	_, err := r.db.DB().Exec(query, formData.Username, formData.ToDate, formData.FromDate,
		formData.ShowExported, formData.ShowHourSalaries, formData.ShowMonthlySalaries,
		formData.Employees, formData.Salaries, formData.ExcludedSalaries, formData.ExportFormat)
	if err != nil {
		return fmt.Errorf("failed to save form data: %w", err)
	}
	
	return nil
}

// DeleteFormData deletes form data for a user
func (r *SQLiteRepository) DeleteFormData(username string) error {
	query := `DELETE FROM form_data WHERE username = ?`
	_, err := r.db.DB().Exec(query, username)
	if err != nil {
		return fmt.Errorf("failed to delete form data: %w", err)
	}
	return nil
}

// CleanupExpiredSessions removes expired sessions
func (r *SQLiteRepository) CleanupExpiredSessions() error {
	return r.db.CleanupExpiredSessions()
}
