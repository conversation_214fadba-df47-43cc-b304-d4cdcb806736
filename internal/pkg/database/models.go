package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"
)

// UserSetting represents a user setting in the database
type UserSetting struct {
	ID           int       `json:"id"`
	Username     string    `json:"username"`
	Setting<PERSON>ey   string    `json:"setting_key"`
	SettingValue string    `json:"setting_value"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// Session represents a user session in the database
type Session struct {
	ID           string    `json:"id"`
	Username     string    `json:"username"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// Company represents a company configuration
type Company struct {
	ID                   int       `json:"id"`
	Name                 string    `json:"name"`
	DisplayName          string    `json:"display_name"`
	KeycloakURL          string    `json:"keycloak_url"`
	KeycloakRealm        string    `json:"keycloak_realm"`
	KeycloakClientID     string    `json:"keycloak_client_id"`
	KeycloakClientSecret string    `json:"keycloak_client_secret"`
	KeycloakRequiredRole string    `json:"keycloak_required_role"`
	Enabled              bool      `json:"enabled"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

// DatabaseConnection represents a Visma database connection configuration
// Each company has exactly one database connection (1:1 relationship)
type DatabaseConnection struct {
	ID           int               `json:"id"`
	CompanyID    int               `json:"company_id"` // Unique - one connection per company
	Server       string            `json:"server"`
	Instance     string            `json:"instance"`
	Port         int               `json:"port"`
	Username     string            `json:"username"`
	Password     string            `json:"password"`
	DatabaseName string            `json:"database_name"`
	Params       map[string]string `json:"params"` // Will be JSON in DB
	Enabled      bool              `json:"enabled"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// UserSession represents a user's current session context
type UserSession struct {
	ID                  int       `json:"id"`
	Username            string    `json:"username"`
	CurrentCompanyID    *int      `json:"current_company_id"`   // Currently selected company
	AccessibleCompanies []int     `json:"accessible_companies"` // Companies user has access to (from JWT claims)
	SessionData         string    `json:"session_data"`         // Additional session data as JSON
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// FormData represents user's form preferences (updated with database_id)
type FormData struct {
	ID                  int       `json:"id"`
	Username            string    `json:"username"`
	DatabaseID          *int      `json:"database_id"` // Nullable foreign key
	ToDate              string    `json:"to_date"`
	FromDate            string    `json:"from_date"`
	ShowExported        bool      `json:"show_exported"`
	ShowHourSalaries    bool      `json:"show_hour_salaries"`
	ShowMonthlySalaries bool      `json:"show_monthly_salaries"`
	Employees           string    `json:"employees"`         // JSON array
	Salaries            string    `json:"salaries"`          // JSON array
	ExcludedSalaries    string    `json:"excluded_salaries"` // JSON array
	ExportFormat        string    `json:"export_format"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// FormDataRequest represents the structure for form data API requests
type FormDataRequest struct {
	ToDate              string   `json:"toDate"`
	FromDate            string   `json:"fromDate"`
	ShowExported        bool     `json:"showExported"`
	ShowHourSalaries    bool     `json:"showHourSalaries"`
	ShowMonthlySalaries bool     `json:"showMonthlySalaries"`
	Employees           []string `json:"employees"`
	Salaries            []string `json:"salaries"`
	ExcludedSalaries    []string `json:"excludedSalaries"`
	ExportFormat        string   `json:"exportFormat"`
}

// ToFormData converts FormDataRequest to FormData model
func (fdr *FormDataRequest) ToFormData(username string) (*FormData, error) {
	employeesJSON, err := json.Marshal(fdr.Employees)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal employees: %w", err)
	}

	salariesJSON, err := json.Marshal(fdr.Salaries)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal salaries: %w", err)
	}

	excludedSalariesJSON, err := json.Marshal(fdr.ExcludedSalaries)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal excluded salaries: %w", err)
	}

	return &FormData{
		Username:            username,
		ToDate:              fdr.ToDate,
		FromDate:            fdr.FromDate,
		ShowExported:        fdr.ShowExported,
		ShowHourSalaries:    fdr.ShowHourSalaries,
		ShowMonthlySalaries: fdr.ShowMonthlySalaries,
		Employees:           string(employeesJSON),
		Salaries:            string(salariesJSON),
		ExcludedSalaries:    string(excludedSalariesJSON),
		ExportFormat:        fdr.ExportFormat,
	}, nil
}

// ToFormDataRequest converts FormData to FormDataRequest
func (fd *FormData) ToFormDataRequest() (*FormDataRequest, error) {
	var employees []string
	if fd.Employees != "" {
		if err := json.Unmarshal([]byte(fd.Employees), &employees); err != nil {
			return nil, fmt.Errorf("failed to unmarshal employees: %w", err)
		}
	}

	var salaries []string
	if fd.Salaries != "" {
		if err := json.Unmarshal([]byte(fd.Salaries), &salaries); err != nil {
			return nil, fmt.Errorf("failed to unmarshal salaries: %w", err)
		}
	}

	var excludedSalaries []string
	if fd.ExcludedSalaries != "" {
		if err := json.Unmarshal([]byte(fd.ExcludedSalaries), &excludedSalaries); err != nil {
			return nil, fmt.Errorf("failed to unmarshal excluded salaries: %w", err)
		}
	}

	return &FormDataRequest{
		ToDate:              fd.ToDate,
		FromDate:            fd.FromDate,
		ShowExported:        fd.ShowExported,
		ShowHourSalaries:    fd.ShowHourSalaries,
		ShowMonthlySalaries: fd.ShowMonthlySalaries,
		Employees:           employees,
		Salaries:            salaries,
		ExcludedSalaries:    excludedSalaries,
		ExportFormat:        fd.ExportFormat,
	}, nil
}

// Repository interface for database operations
type Repository interface {
	// User Settings
	GetUserSetting(username, key string) (*UserSetting, error)
	SetUserSetting(username, key, value string) error
	DeleteUserSetting(username, key string) error

	// Sessions
	GetSession(sessionID string) (*Session, error)
	CreateSession(session *Session) error
	UpdateSession(session *Session) error
	DeleteSession(sessionID string) error
	DeleteUserSessions(username string) error

	// Companies
	GetCompanies() ([]*Company, error)
	GetCompany(id int) (*Company, error)
	GetCompanyByName(name string) (*Company, error)
	CreateCompany(company *Company) error
	UpdateCompany(company *Company) error
	DeleteCompany(id int) error

	// Database Connections (1:1 with companies)
	GetDatabaseConnectionByCompany(companyID int) (*DatabaseConnection, error)
	GetDatabaseConnection(id int) (*DatabaseConnection, error)
	CreateDatabaseConnection(conn *DatabaseConnection) error
	UpdateDatabaseConnection(conn *DatabaseConnection) error
	DeleteDatabaseConnection(id int) error

	// User Sessions
	GetUserSession(username string) (*UserSession, error)
	CreateUserSession(session *UserSession) error
	UpdateUserSession(session *UserSession) error
	DeleteUserSession(username string) error

	// Form Data (updated to support database_id)
	GetFormData(username string, databaseID *int) (*FormData, error)
	SaveFormData(formData *FormData) error
	DeleteFormData(username string, databaseID *int) error

	// Cleanup
	CleanupExpiredSessions() error
}

// SQLiteRepository implements Repository interface for SQLite
type SQLiteRepository struct {
	db *SQLiteDB
}

// NewSQLiteRepository creates a new SQLite repository
func NewSQLiteRepository(db *SQLiteDB) Repository {
	return &SQLiteRepository{db: db}
}

// GetUserSetting retrieves a user setting
func (r *SQLiteRepository) GetUserSetting(username, key string) (*UserSetting, error) {
	query := `SELECT id, username, setting_key, setting_value, created_at, updated_at 
			  FROM user_settings WHERE username = ? AND setting_key = ?`

	var setting UserSetting
	err := r.db.DB().QueryRow(query, username, key).Scan(
		&setting.ID, &setting.Username, &setting.SettingKey, &setting.SettingValue,
		&setting.CreatedAt, &setting.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get user setting: %w", err)
	}

	return &setting, nil
}

// SetUserSetting creates or updates a user setting
func (r *SQLiteRepository) SetUserSetting(username, key, value string) error {
	query := `INSERT INTO user_settings (username, setting_key, setting_value, updated_at)
			  VALUES (?, ?, ?, datetime('now'))
			  ON CONFLICT(username, setting_key) DO UPDATE SET
			  setting_value = excluded.setting_value,
			  updated_at = datetime('now')`

	_, err := r.db.DB().Exec(query, username, key, value)
	if err != nil {
		return fmt.Errorf("failed to set user setting: %w", err)
	}

	return nil
}

// DeleteUserSetting deletes a user setting
func (r *SQLiteRepository) DeleteUserSetting(username, key string) error {
	query := `DELETE FROM user_settings WHERE username = ? AND setting_key = ?`
	_, err := r.db.DB().Exec(query, username, key)
	if err != nil {
		return fmt.Errorf("failed to delete user setting: %w", err)
	}
	return nil
}

// GetSession retrieves a session by ID
func (r *SQLiteRepository) GetSession(sessionID string) (*Session, error) {
	query := `SELECT id, username, access_token, refresh_token, expires_at, created_at, updated_at
			  FROM sessions WHERE id = ? AND expires_at > datetime('now')`

	var session Session
	err := r.db.DB().QueryRow(query, sessionID).Scan(
		&session.ID, &session.Username, &session.AccessToken, &session.RefreshToken,
		&session.ExpiresAt, &session.CreatedAt, &session.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	return &session, nil
}

// CreateSession creates a new session
func (r *SQLiteRepository) CreateSession(session *Session) error {
	query := `INSERT INTO sessions (id, username, access_token, refresh_token, expires_at)
			  VALUES (?, ?, ?, ?, ?)`

	_, err := r.db.DB().Exec(query, session.ID, session.Username, session.AccessToken,
		session.RefreshToken, session.ExpiresAt)
	if err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}

	return nil
}

// UpdateSession updates an existing session
func (r *SQLiteRepository) UpdateSession(session *Session) error {
	query := `UPDATE sessions SET access_token = ?, refresh_token = ?, expires_at = ?, 
			  updated_at = datetime('now') WHERE id = ?`

	_, err := r.db.DB().Exec(query, session.AccessToken, session.RefreshToken,
		session.ExpiresAt, session.ID)
	if err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}

	return nil
}

// DeleteSession deletes a session
func (r *SQLiteRepository) DeleteSession(sessionID string) error {
	query := `DELETE FROM sessions WHERE id = ?`
	_, err := r.db.DB().Exec(query, sessionID)
	if err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}
	return nil
}

// DeleteUserSessions deletes all sessions for a user
func (r *SQLiteRepository) DeleteUserSessions(username string) error {
	query := `DELETE FROM sessions WHERE username = ?`
	_, err := r.db.DB().Exec(query, username)
	if err != nil {
		return fmt.Errorf("failed to delete user sessions: %w", err)
	}
	return nil
}

// GetFormData retrieves form data for a user and specific database
func (r *SQLiteRepository) GetFormData(username string, databaseID *int) (*FormData, error) {
	var query string
	var args []interface{}

	if databaseID == nil {
		query = `SELECT id, username, database_id, to_date, from_date, show_exported, show_hour_salaries,
				 show_monthly_salaries, employees, salaries, excluded_salaries, export_format,
				 created_at, updated_at FROM form_data WHERE username = ? AND database_id IS NULL`
		args = []interface{}{username}
	} else {
		query = `SELECT id, username, database_id, to_date, from_date, show_exported, show_hour_salaries,
				 show_monthly_salaries, employees, salaries, excluded_salaries, export_format,
				 created_at, updated_at FROM form_data WHERE username = ? AND database_id = ?`
		args = []interface{}{username, *databaseID}
	}

	var formData FormData
	err := r.db.DB().QueryRow(query, args...).Scan(
		&formData.ID, &formData.Username, &formData.DatabaseID, &formData.ToDate, &formData.FromDate,
		&formData.ShowExported, &formData.ShowHourSalaries, &formData.ShowMonthlySalaries,
		&formData.Employees, &formData.Salaries, &formData.ExcludedSalaries,
		&formData.ExportFormat, &formData.CreatedAt, &formData.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get form data: %w", err)
	}

	return &formData, nil
}

// SaveFormData creates or updates form data for a user
func (r *SQLiteRepository) SaveFormData(formData *FormData) error {
	query := `INSERT INTO form_data (username, database_id, to_date, from_date, show_exported,
			  show_hour_salaries, show_monthly_salaries, employees, salaries,
			  excluded_salaries, export_format, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
			  ON CONFLICT(username, database_id) DO UPDATE SET
			  to_date = excluded.to_date,
			  from_date = excluded.from_date,
			  show_exported = excluded.show_exported,
			  show_hour_salaries = excluded.show_hour_salaries,
			  show_monthly_salaries = excluded.show_monthly_salaries,
			  employees = excluded.employees,
			  salaries = excluded.salaries,
			  excluded_salaries = excluded.excluded_salaries,
			  export_format = excluded.export_format,
			  updated_at = datetime('now')`

	_, err := r.db.DB().Exec(query, formData.Username, formData.DatabaseID, formData.ToDate, formData.FromDate,
		formData.ShowExported, formData.ShowHourSalaries, formData.ShowMonthlySalaries,
		formData.Employees, formData.Salaries, formData.ExcludedSalaries, formData.ExportFormat)
	if err != nil {
		return fmt.Errorf("failed to save form data: %w", err)
	}

	return nil
}

// DeleteFormData deletes form data for a user and specific database
func (r *SQLiteRepository) DeleteFormData(username string, databaseID *int) error {
	var query string
	var args []interface{}

	if databaseID == nil {
		query = `DELETE FROM form_data WHERE username = ? AND database_id IS NULL`
		args = []interface{}{username}
	} else {
		query = `DELETE FROM form_data WHERE username = ? AND database_id = ?`
		args = []interface{}{username, *databaseID}
	}

	_, err := r.db.DB().Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to delete form data: %w", err)
	}
	return nil
}

// CleanupExpiredSessions removes expired sessions
func (r *SQLiteRepository) CleanupExpiredSessions() error {
	return r.db.CleanupExpiredSessions()
}

// Company methods

// GetCompanies retrieves all companies
func (r *SQLiteRepository) GetCompanies() ([]*Company, error) {
	query := `SELECT id, name, display_name, keycloak_url, keycloak_realm, keycloak_client_id,
			  keycloak_client_secret, keycloak_required_role, enabled, created_at, updated_at
			  FROM companies WHERE enabled = 1 ORDER BY display_name`

	rows, err := r.db.DB().Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query companies: %w", err)
	}
	defer rows.Close()

	var companies []*Company
	for rows.Next() {
		company := &Company{}
		err := rows.Scan(&company.ID, &company.Name, &company.DisplayName, &company.KeycloakURL,
			&company.KeycloakRealm, &company.KeycloakClientID, &company.KeycloakClientSecret,
			&company.KeycloakRequiredRole, &company.Enabled, &company.CreatedAt, &company.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan company: %w", err)
		}
		companies = append(companies, company)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating companies: %w", err)
	}

	return companies, nil
}

// GetCompany retrieves a company by ID
func (r *SQLiteRepository) GetCompany(id int) (*Company, error) {
	query := `SELECT id, name, display_name, keycloak_url, keycloak_realm, keycloak_client_id,
			  keycloak_client_secret, keycloak_required_role, enabled, created_at, updated_at
			  FROM companies WHERE id = ?`

	company := &Company{}
	err := r.db.DB().QueryRow(query, id).Scan(&company.ID, &company.Name, &company.DisplayName,
		&company.KeycloakURL, &company.KeycloakRealm, &company.KeycloakClientID, &company.KeycloakClientSecret,
		&company.KeycloakRequiredRole, &company.Enabled, &company.CreatedAt, &company.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get company: %w", err)
	}

	return company, nil
}

// GetCompanyByName retrieves a company by name
func (r *SQLiteRepository) GetCompanyByName(name string) (*Company, error) {
	query := `SELECT id, name, display_name, keycloak_url, keycloak_realm, keycloak_client_id,
			  keycloak_client_secret, keycloak_required_role, enabled, created_at, updated_at
			  FROM companies WHERE name = ?`

	company := &Company{}
	err := r.db.DB().QueryRow(query, name).Scan(&company.ID, &company.Name, &company.DisplayName,
		&company.KeycloakURL, &company.KeycloakRealm, &company.KeycloakClientID, &company.KeycloakClientSecret,
		&company.KeycloakRequiredRole, &company.Enabled, &company.CreatedAt, &company.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get company by name: %w", err)
	}

	return company, nil
}

// CreateCompany creates a new company
func (r *SQLiteRepository) CreateCompany(company *Company) error {
	query := `INSERT INTO companies (name, display_name, keycloak_url, keycloak_realm, keycloak_client_id,
			  keycloak_client_secret, keycloak_required_role, enabled)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := r.db.DB().Exec(query, company.Name, company.DisplayName, company.KeycloakURL,
		company.KeycloakRealm, company.KeycloakClientID, company.KeycloakClientSecret,
		company.KeycloakRequiredRole, company.Enabled)
	if err != nil {
		return fmt.Errorf("failed to create company: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get company ID: %w", err)
	}

	company.ID = int(id)
	return nil
}

// UpdateCompany updates an existing company
func (r *SQLiteRepository) UpdateCompany(company *Company) error {
	query := `UPDATE companies SET name = ?, display_name = ?, keycloak_url = ?, keycloak_realm = ?,
			  keycloak_client_id = ?, keycloak_client_secret = ?, keycloak_required_role = ?,
			  enabled = ?, updated_at = datetime('now') WHERE id = ?`

	_, err := r.db.DB().Exec(query, company.Name, company.DisplayName, company.KeycloakURL,
		company.KeycloakRealm, company.KeycloakClientID, company.KeycloakClientSecret,
		company.KeycloakRequiredRole, company.Enabled, company.ID)
	if err != nil {
		return fmt.Errorf("failed to update company: %w", err)
	}

	return nil
}

// DeleteCompany deletes a company
func (r *SQLiteRepository) DeleteCompany(id int) error {
	query := `DELETE FROM companies WHERE id = ?`
	_, err := r.db.DB().Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete company: %w", err)
	}
	return nil
}

// Database Connection methods

// GetDatabaseConnectionByCompany retrieves the database connection for a specific company
func (r *SQLiteRepository) GetDatabaseConnectionByCompany(companyID int) (*DatabaseConnection, error) {
	query := `SELECT id, company_id, server, instance, port, username, password,
			  database_name, params, enabled, created_at, updated_at
			  FROM database_connections WHERE company_id = ? AND enabled = 1`

	conn := &DatabaseConnection{}
	var paramsJSON string
	err := r.db.DB().QueryRow(query, companyID).Scan(&conn.ID, &conn.CompanyID, &conn.Server,
		&conn.Instance, &conn.Port, &conn.Username, &conn.Password, &conn.DatabaseName,
		&paramsJSON, &conn.Enabled, &conn.CreatedAt, &conn.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection by company: %w", err)
	}

	// Parse JSON params
	if paramsJSON != "" {
		if err := json.Unmarshal([]byte(paramsJSON), &conn.Params); err != nil {
			return nil, fmt.Errorf("failed to parse params JSON: %w", err)
		}
	} else {
		conn.Params = make(map[string]string)
	}

	return conn, nil
}

// GetDatabaseConnection retrieves a database connection by ID
func (r *SQLiteRepository) GetDatabaseConnection(id int) (*DatabaseConnection, error) {
	query := `SELECT id, company_id, server, instance, port, username, password,
			  database_name, params, enabled, created_at, updated_at
			  FROM database_connections WHERE id = ?`

	conn := &DatabaseConnection{}
	var paramsJSON string
	err := r.db.DB().QueryRow(query, id).Scan(&conn.ID, &conn.CompanyID,
		&conn.Server, &conn.Instance, &conn.Port, &conn.Username, &conn.Password, &conn.DatabaseName,
		&paramsJSON, &conn.Enabled, &conn.CreatedAt, &conn.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	// Parse JSON params
	if paramsJSON != "" {
		if err := json.Unmarshal([]byte(paramsJSON), &conn.Params); err != nil {
			return nil, fmt.Errorf("failed to parse params JSON: %w", err)
		}
	} else {
		conn.Params = make(map[string]string)
	}

	return conn, nil
}

// CreateDatabaseConnection creates a new database connection
func (r *SQLiteRepository) CreateDatabaseConnection(conn *DatabaseConnection) error {
	// Marshal params to JSON
	paramsJSON, err := json.Marshal(conn.Params)
	if err != nil {
		return fmt.Errorf("failed to marshal params: %w", err)
	}

	query := `INSERT INTO database_connections (company_id, server, instance, port,
			  username, password, database_name, params, enabled)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := r.db.DB().Exec(query, conn.CompanyID, conn.Server,
		conn.Instance, conn.Port, conn.Username, conn.Password, conn.DatabaseName, string(paramsJSON), conn.Enabled)
	if err != nil {
		return fmt.Errorf("failed to create database connection: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get database connection ID: %w", err)
	}

	conn.ID = int(id)
	return nil
}

// UpdateDatabaseConnection updates an existing database connection
func (r *SQLiteRepository) UpdateDatabaseConnection(conn *DatabaseConnection) error {
	// Marshal params to JSON
	paramsJSON, err := json.Marshal(conn.Params)
	if err != nil {
		return fmt.Errorf("failed to marshal params: %w", err)
	}

	query := `UPDATE database_connections SET company_id = ?, server = ?,
			  instance = ?, port = ?, username = ?, password = ?, database_name = ?, params = ?,
			  enabled = ?, updated_at = datetime('now') WHERE id = ?`

	_, err = r.db.DB().Exec(query, conn.CompanyID, conn.Server,
		conn.Instance, conn.Port, conn.Username, conn.Password, conn.DatabaseName, string(paramsJSON),
		conn.Enabled, conn.ID)
	if err != nil {
		return fmt.Errorf("failed to update database connection: %w", err)
	}

	return nil
}

// DeleteDatabaseConnection deletes a database connection
func (r *SQLiteRepository) DeleteDatabaseConnection(id int) error {
	query := `DELETE FROM database_connections WHERE id = ?`
	_, err := r.db.DB().Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete database connection: %w", err)
	}
	return nil
}

// User Session methods

// GetUserSession retrieves a user session by username
func (r *SQLiteRepository) GetUserSession(username string) (*UserSession, error) {
	query := `SELECT id, username, current_company_id, accessible_companies, session_data,
			  created_at, updated_at FROM user_sessions WHERE username = ?`

	session := &UserSession{}
	var accessibleCompaniesJSON string
	err := r.db.DB().QueryRow(query, username).Scan(&session.ID, &session.Username,
		&session.CurrentCompanyID, &accessibleCompaniesJSON, &session.SessionData,
		&session.CreatedAt, &session.UpdatedAt)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get user session: %w", err)
	}

	// Parse JSON accessible companies
	if accessibleCompaniesJSON != "" {
		if err := json.Unmarshal([]byte(accessibleCompaniesJSON), &session.AccessibleCompanies); err != nil {
			return nil, fmt.Errorf("failed to parse accessible companies JSON: %w", err)
		}
	} else {
		session.AccessibleCompanies = []int{}
	}

	return session, nil
}

// CreateUserSession creates a new user session
func (r *SQLiteRepository) CreateUserSession(session *UserSession) error {
	// Marshal accessible companies to JSON
	accessibleCompaniesJSON, err := json.Marshal(session.AccessibleCompanies)
	if err != nil {
		return fmt.Errorf("failed to marshal accessible companies: %w", err)
	}

	query := `INSERT INTO user_sessions (username, current_company_id, accessible_companies, session_data)
			  VALUES (?, ?, ?, ?)`

	result, err := r.db.DB().Exec(query, session.Username, session.CurrentCompanyID,
		string(accessibleCompaniesJSON), session.SessionData)
	if err != nil {
		return fmt.Errorf("failed to create user session: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get user session ID: %w", err)
	}

	session.ID = int(id)
	return nil
}

// UpdateUserSession updates an existing user session
func (r *SQLiteRepository) UpdateUserSession(session *UserSession) error {
	// Marshal accessible companies to JSON
	accessibleCompaniesJSON, err := json.Marshal(session.AccessibleCompanies)
	if err != nil {
		return fmt.Errorf("failed to marshal accessible companies: %w", err)
	}

	query := `UPDATE user_sessions SET current_company_id = ?, accessible_companies = ?,
			  session_data = ?, updated_at = datetime('now') WHERE username = ?`

	_, err = r.db.DB().Exec(query, session.CurrentCompanyID, string(accessibleCompaniesJSON),
		session.SessionData, session.Username)
	if err != nil {
		return fmt.Errorf("failed to update user session: %w", err)
	}

	return nil
}

// DeleteUserSession deletes a user session
func (r *SQLiteRepository) DeleteUserSession(username string) error {
	query := `DELETE FROM user_sessions WHERE username = ?`
	_, err := r.db.DB().Exec(query, username)
	if err != nil {
		return fmt.Errorf("failed to delete user session: %w", err)
	}
	return nil
}
