package vismadb

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
)

// OrdLn table in visma database
type OrdLn struct {
	OrdNo   int
	LnNo    int
	TrDt    int
	EmpNo   int
	WgYr    int
	RunNo   int
	Qty     float64
	Rate    int
	Am      int
	AcNo    int
	AgAcNo  int
	DedCd   int
	Bas     float64
	R1      int
	R2      int
	R3      int
	R4      int
	R5      int
	R6      int
	Free4   float64
	WageSrt string
}

// GetNewFree4 returns a new int to be used for updates to Free4 column in OrdLn from visma database
func (c *Client) GetNewFree4() (int, error) {
	free4 := 0

	query := "SELECT MAX(Free4) FROM OrdLn WHERE Free4 > @Free4"

	// Debug query information
	slog.Debug("visma.GetNewFree4", "query", strings.NewReplacer("@Free4", strconv.Itoa(free4)).Replace(query))

	stmt, err := c.Conn.PrepareContext(context.TODO(), "SELECT MAX(Free4) FROM OrdLn WHERE Free4 > @Free4")
	if err != nil {
		return 0, fmt.Errorf("prepare failed, %s", err)
	}
	defer stmt.Close()

	row := stmt.QueryRowContext(context.TODO(), sql.Named("Free4", free4))

	var maxFree4Result []uint8

	err = row.Scan(&maxFree4Result)
	switch {
	case err == sql.ErrNoRows:
		return 0, fmt.Errorf("no result")
	case err != nil:
		return 0, fmt.Errorf("scan failed, %s", err)
	}

	// TODO: This part is really shitty, fix please
	maxFree4String := strings.Split(string(maxFree4Result), ".")
	maxFree4, err := strconv.Atoi(maxFree4String[0])
	if err != nil {
		return 0, err
	}

	if maxFree4 == 0 {
		return 0, fmt.Errorf("failed to get current Free4 from database")
	}

	return maxFree4 + 1, err
}

// UpdateFree4 updates the orderline(s) Free4 column in OrdLn table from visma database
func (c *Client) UpdateFree4(free4 int, orderLineIDs map[int][]int) (int, error) {
	// Build tuples for IN clause: (OrdNo, LnNo)
	var tuples []string
	for orderNo, lnNos := range orderLineIDs {
		for _, lnNo := range lnNos {
			tuples = append(tuples, fmt.Sprintf("(%d,%d)", orderNo, lnNo))
		}
	}

	query := "UPDATE OrdLn SET Free4 = @Free4 WHERE Free4 = 0 AND EXISTS (" +
		"SELECT 1 FROM (VALUES " + strings.Join(tuples, ",") + ") AS t(OrdNo,LnNo) " +
		"WHERE t.OrdNo = OrdLn.OrdNo AND t.LnNo = OrdLn.LnNo)"

	// Debug query information
	slog.Debug("visma.UpdateFree4", "query", strings.NewReplacer("@Free4", strconv.Itoa(free4)).Replace(query))

	stmt, err := c.Conn.PrepareContext(context.TODO(), query)
	if err != nil {
		return 0, fmt.Errorf("prepare failed, %s", err)
	}
	defer stmt.Close()

	result, err := stmt.ExecContext(context.TODO(), sql.Named("Free4", free4))
	if err != nil {
		return 0, err
	}
	affected, err := result.RowsAffected()
	if err != nil {
		return 0, err
	}

	return int(affected), err
}

// GetHourSalaries returns a list of all hourly salaries
func (c *Client) GetHourSalaries(employees, fromDate, toDate string, prevTransactions bool) (*[]OrdLn, map[int][]int, error) {
	free4 := 0
	transGr := 9

	if err := c.Conn.Ping(); err != nil {
		return nil, nil, err
	}

	querySelect := "SELECT OL.OrdNo AS OrdNo, OL.LnNo AS LnNo, OL.Free4 AS Free4, OL.EmpNo AS EmpNo, OL.WageSrt AS WgSrt, year(convert(varchar,max(OL.TrDt),112)) AS WgYr, " +
		"Sum(OL.NoInvoAb+OL.NoFin) AS Qty, OL.TrDt AS TrDt FROM OrdLn OL, EmpV E WHERE OL.WageSrt > '' AND (OL.TrDt >= @fromDate AND OL.TrDt <= @toDate) " +
		"AND OL.EmpNo > 0 AND OL.TransGr = @TransGr AND OL.EmpNo = E.EmpNo AND E.EmpPrGr = '' AND OL.EmpNo IN (@EmpNo) " +
		"AND OL.Free4 = @Free4"

	queryGroupBy := "Group By OL.EmpNo, OL.TrDt, OL.TransGr, OL.WageSrt, OL.R1, OL.R2, OL.R4, OL.R5, OL.R6, OL.OrdNo, OL.Free4, OL.LnNo"
	queryOrderBy := "ORDER BY OL.EmpNo, OL.TrDt ASC"

	if prevTransactions {
		querySelect = strings.Replace(querySelect, "AND OL.Free4 = @Free4", "", 1)
	}
	if employees != "" {
		querySelect = strings.Replace(querySelect, "AND OL.EmpNo IN (@EmpNo)", fmt.Sprintf("AND OL.EmpNo IN (%s)", employees), 1)
	} else {
		querySelect = strings.Replace(querySelect, "AND OL.EmpNo IN (@EmpNo)", "", 1)
	}

	// Combine the query parts
	query := querySelect + " " + queryGroupBy + " " + queryOrderBy

	// Debug query information
	slog.Debug("getting hour salaries",
		"query", strings.NewReplacer("@fromDate", fromDate,
			"@toDate", toDate,
			"@Free4", strconv.Itoa(free4),
			"@TransGr", strconv.Itoa(transGr)).Replace(query),
	)

	stmt, err := c.Conn.PrepareContext(context.TODO(), query)
	if err != nil {
		return nil, nil, fmt.Errorf("prepare failed, %s", err)
	}
	defer stmt.Close()

	rows, err := stmt.QueryContext(context.TODO(), sql.Named("fromDate", fromDate), sql.Named("toDate", toDate),
		sql.Named("Free4", free4), sql.Named("TransGr", transGr))
	switch {
	case err == sql.ErrNoRows:
		return nil, nil, fmt.Errorf("no result")
	case err != nil:
		return nil, nil, fmt.Errorf("query failed, %s", err)
	}
	defer rows.Close()

	orderLines := []OrdLn{}
	orderLineIDs := make(map[int][]int)

	for rows.Next() {
		orderline := OrdLn{}
		err = rows.Scan(&orderline.OrdNo, &orderline.LnNo, &orderline.Free4, &orderline.EmpNo, &orderline.WageSrt, &orderline.WgYr, &orderline.Qty, &orderline.TrDt)
		if err != nil {
			return nil, nil, fmt.Errorf("scan failed, %s", err)
		}

		// Simply append each line to the results
		orderLines = append(orderLines, orderline)

		// Store the orderline IDs
		orderLineIDs[orderline.OrdNo] = append(orderLineIDs[orderline.OrdNo], orderline.LnNo)
	}

	return &orderLines, orderLineIDs, err
}

// GetMonthlySalaries returns a list of all monthly salaries
func (c *Client) GetMonthlySalaries(employees, salaries, excludedSalaries, fromDate, toDate string, prevTransactions bool) (*[]OrdLn, map[int][]int, error) {
	free4 := 0
	transGr := 9
	empPrGr := 1

	if err := c.Conn.Ping(); err != nil {
		return nil, nil, err
	}

	querySelect := "SELECT OL.OrdNo AS OrdNo, OL.LnNo AS LnNo, OL.Free4 AS Free4, OL.EmpNo AS EmpNo, OL.WageSrt AS WgSrt, year(convert(varchar,max(OL.TrDt),112)) AS WgYr, " +
		"Sum(OL.NoInvoAb+OL.NoFin) AS Qty, OL.TrDt AS TrDt FROM OrdLn OL, EmpV E WHERE OL.WageSrt > '' AND (OL.TrDt >= @fromDate AND OL.TrDt <= @toDate) " +
		"AND OL.EmpNo > 0 AND OL.TransGr = @TransGr AND OL.EmpNo = E.EmpNo AND E.EmpPrGr = @EmpPrGr AND OL.EmpNo IN (@EmpNo) " +
		"AND OL.Free4 = @Free4"

	queryGroupBy := "Group By OL.EmpNo, OL.TrDt, OL.TransGr, OL.WageSrt, OL.R1, OL.R2, OL.R4, OL.R5, OL.R6, OL.OrdNo, OL.Free4, OL.LnNo"
	queryOrderBy := "ORDER BY OL.EmpNo, OL.TrDt ASC"

	if prevTransactions {
		querySelect = strings.Replace(querySelect, "AND OL.Free4 = @Free4", "", 1)
	}
	if employees != "" {
		querySelect = strings.Replace(querySelect, "AND OL.EmpNo IN (@EmpNo)", fmt.Sprintf("AND OL.EmpNo IN (%s)", employees), 1)
	} else {
		querySelect = strings.Replace(querySelect, "AND OL.EmpNo IN (@EmpNo)", "", 1)
	}

	// Handle salary filtering
	var salaryConditions []string
	if salaries != "" {
		salaryConditions = append(salaryConditions, fmt.Sprintf("OL.WageSrt IN (%s)", salaries))
	}
	if excludedSalaries != "" {
		salaryConditions = append(salaryConditions, fmt.Sprintf("OL.WageSrt NOT IN (%s)", excludedSalaries))
	}

	// Add the salary conditions to the query if we have any
	if len(salaryConditions) > 0 {
		querySelect += " AND " + strings.Join(salaryConditions, " AND ")
	}

	// Combine the query parts
	query := querySelect + " " + queryGroupBy + " " + queryOrderBy

	// Debug query information
	slog.Debug("getting monthly salaries",
		"salaries", salaries,
		"excludedSalaries", excludedSalaries,
		"salaryConditions", salaryConditions,
		"query", strings.NewReplacer("@fromDate", fromDate,
			"@toDate", toDate,
			"@Free4", strconv.Itoa(free4),
			"@TransGr", strconv.Itoa(transGr),
			"@EmpPrGr", strconv.Itoa(empPrGr)).Replace(query),
	)

	stmt, err := c.Conn.PrepareContext(context.TODO(), query)
	if err != nil {
		return nil, nil, fmt.Errorf("prepare failed, %s", err)
	}
	defer stmt.Close()

	rows, err := stmt.QueryContext(context.TODO(), sql.Named("fromDate", fromDate), sql.Named("toDate", toDate),
		sql.Named("Free4", free4), sql.Named("TransGr", transGr), sql.Named("EmpPrGr", empPrGr))
	switch {
	case err == sql.ErrNoRows:
		return nil, nil, fmt.Errorf("no result")
	case err != nil:
		return nil, nil, fmt.Errorf("query failed, %s", err)
	}
	defer rows.Close()

	orderLineIDs := make(map[int][]int)
	orderLines := []OrdLn{}

	for rows.Next() {
		orderline := OrdLn{}
		err = rows.Scan(&orderline.OrdNo, &orderline.LnNo, &orderline.Free4, &orderline.EmpNo, &orderline.WageSrt, &orderline.WgYr, &orderline.Qty, &orderline.TrDt)
		if err != nil {
			return nil, nil, fmt.Errorf("scan failed, %s", err)
		}

		// Simply append each line to the results
		orderLines = append(orderLines, orderline)

		// Store the orderline IDs
		orderLineIDs[orderline.OrdNo] = append(orderLineIDs[orderline.OrdNo], orderline.LnNo)
	}

	return &orderLines, orderLineIDs, err
}
