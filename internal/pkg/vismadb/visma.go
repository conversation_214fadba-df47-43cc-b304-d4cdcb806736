package vismadb

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"log/slog"
	"net/url"
	"strings"

	// Blank import cause reasons
	_ "github.com/denisenkom/go-mssqldb"
)

// Client struct to store all things visma
type Client struct {
	Server   string
	Instance string
	Port     int
	User     string
	Password string
	Database string
	Params   map[string]string
	Conn     *sql.DB
}

type ClientConfig struct {
	Server   string
	Instance string
	Port     int
	Username string
	Password string
	Database string
	Params   map[string]string
}

// NewClient returns a client to work with the visma database
func NewClient(cfg *ClientConfig) (*Client, error) {
	client := &Client{
		Server:   cfg.Server,
		Instance: cfg.Instance,
		Port:     cfg.Port,
		User:     cfg.Username,
		Password: cfg.Password,
		Database: cfg.Database,
		Params:   cfg.Params,
	}

	query := url.Values{}
	query.Add("database", cfg.Database)
	for k, v := range cfg.Params {
		query.Add(k, v)
	}

	u := &url.URL{
		Scheme:   "sqlserver",
		User:     url.UserPassword(cfg.Username, cfg.Password),
		Host:     fmt.Sprintf("%s:%d", cfg.Server, cfg.Port),
		RawQuery: query.Encode(),
		Path:     cfg.Instance,
	}

	slog.Debug("visma.NewClient", "connection-string", u.String())

	db, err := sql.Open("sqlserver", u.String())
	if err != nil {
		return nil, err
	}
	client.Conn = db

	return client, err
}

// Close cleans up connections etc to the database
func (c *Client) Close() {
	c.Conn.Close()
}

// StringArray is a type implementing the sql/driver/value interface
// This is due to the native driver not supporting arrays...
type StringArray []string

// Value returns the driver compatible value
func (a StringArray) Value() (driver.Value, error) {
	return "(" + strings.Join(a, ",") + ")", nil
}
