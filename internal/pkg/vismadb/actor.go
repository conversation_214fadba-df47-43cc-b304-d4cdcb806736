package vismadb

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
)

// Actor table in visma database
type Actor struct {
	EmpNo int
	Nm    string
}

// GetActors gets all the employees from the Actor table in visma database
func (c *Client) GetActors() (*[]Actor, error) {
	empNo := 0
	empNoMax := 10000
	query := "SELECT EmpNo, Nm FROM Actor WHERE EmpNo > @EmpNo AND EmpNo < @EmpNoMax"

	// Debug query information
	slog.Debug("visma.GetActors", "query", strings.NewReplacer("@EmpNo", strconv.Itoa(empNo), "@EmpNoMax", strconv.Itoa(empNoMax)).Replace(query))

	stmt, err := c.Conn.PrepareContext(context.TODO(), query)
	if err != nil {
		return nil, fmt.Errorf("prepare failed, %s", err)
	}
	defer stmt.Close()

	rows, err := stmt.Query(sql.Named("EmpNo", empNo), sql.Named("EmpNoMax", empNoMax))
	switch {
	case err == sql.ErrNoRows:
		return nil, fmt.Errorf("no result")
	case err != nil:
		return nil, fmt.Errorf("query failed, %s", err)
	}
	defer rows.Close()

	actors := []Actor{}
	for rows.Next() {
		actor := Actor{}
		err = rows.Scan(&actor.EmpNo, &actor.Nm)
		if err != nil {
			return nil, fmt.Errorf("scan failed, %s", err)
		}
		actors = append(actors, actor)
	}

	return &actors, err
}

// GetActorByEmpNo gets the employee from the Actor table by the EmpNo column in visma database
func (c *Client) GetActorByEmpNo(empNo int) (*Actor, error) {
	empNoMax := 10000
	query := "SELECT EmpNo, Nm FROM Actor WHERE EmpNo > @EmpNo AND EmpNo < @EmpNoMax"

	// Debug query information
	slog.Debug("visma.GetActorByEmpNo", "query", strings.NewReplacer("@EmpNo", strconv.Itoa(empNo), "@EmpNoMax", strconv.Itoa(empNoMax)).Replace(query))

	stmt, err := c.Conn.PrepareContext(context.TODO(), query)
	if err != nil {
		return nil, fmt.Errorf("prepare failed, %s", err)
	}
	defer stmt.Close()

	row := stmt.QueryRowContext(context.TODO(), sql.Named("EmpNo", empNo), sql.Named("EmpNoMax", empNoMax))

	var actor Actor
	err = row.Scan(&actor.EmpNo, &actor.Nm)
	switch {
	case err == sql.ErrNoRows:
		return nil, fmt.Errorf("no result")
	case err != nil:
		return nil, fmt.Errorf("query failed, %s", err)
	}

	return &actor, err
}
