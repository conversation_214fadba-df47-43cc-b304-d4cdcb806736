package middleware

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"log/slog"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/sessions"
)

const (
	// SessionName is the name of the session cookie
	SessionName = "erv-lonn-session"
	// SessionContextKey is the key used to store the session in the request context
	SessionContextKey contextKey = "session"
)

// SessionConfig holds the configuration for session middleware
type SessionConfig struct {
	SecretKey string
	MaxAge    int // in seconds
	Secure    bool
	HttpOnly  bool
	SameSite  http.SameSite
}

// SessionStore wraps the gorilla sessions store
type SessionStore struct {
	store *sessions.CookieStore
}

// NewSessionStore creates a new session store
func NewSessionStore(config *SessionConfig) *SessionStore {
	// Generate a random secret key if not provided
	secretKey := config.SecretKey
	if secretKey == "" {
		secretKey = generateRandomKey()
		slog.Warn("No session secret key provided, generated random key. Sessions will not persist across restarts.")
	}

	store := sessions.NewCookieStore([]byte(secretKey))
	store.Options = &sessions.Options{
		Path:     "/",
		MaxAge:   config.MaxAge,
		HttpOnly: config.HttpOnly,
		Secure:   config.Secure,
		SameSite: config.SameSite,
	}

	return &SessionStore{
		store: store,
	}
}

// generateRandomKey generates a random 32-byte key for session encryption
func generateRandomKey() string {
	key := make([]byte, 32)
	_, err := rand.Read(key)
	if err != nil {
		panic("failed to generate random session key: " + err.Error())
	}
	return base64.StdEncoding.EncodeToString(key)
}

// NewSessionMiddleware creates a new session middleware
func NewSessionMiddleware(sessionStore *SessionStore) func(http.HandlerFunc) http.HandlerFunc {
	return func(h http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// Get the session
			session, err := sessionStore.store.Get(r, SessionName)
			if err != nil {
				slog.Warn("Failed to get session", "error", err)
				// Create a new session if we can't get the existing one
				session, _ = sessionStore.store.New(r, SessionName)
			}

			// Debug session state
			hasValues := len(session.Values) > 0
			isAuthenticated := false
			if auth, ok := session.Values["authenticated"].(bool); ok {
				isAuthenticated = auth
			}

			slog.Debug("Session middleware",
				"session_exists", session != nil,
				"session_is_new", session.IsNew,
				"has_values", hasValues,
				"is_authenticated", isAuthenticated,
				"path", r.URL.Path)

			// Store session in context
			ctx := context.WithValue(r.Context(), SessionContextKey, session)

			// Process the request with the session context
			h.ServeHTTP(w, r.WithContext(ctx))
		}
	}
}

// GetSession retrieves the session from the request context
func GetSession(r *http.Request) *sessions.Session {
	if session, ok := r.Context().Value(SessionContextKey).(*sessions.Session); ok {
		return session
	}
	return nil
}

// SaveSession saves the session
func SaveSession(r *http.Request, w http.ResponseWriter, session *sessions.Session) error {
	slog.Debug("Saving session", "session_is_new", session.IsNew, "session_values_count", len(session.Values))
	err := session.Save(r, w)
	if err != nil {
		slog.Error("Failed to save session", "error", err)
	} else {
		slog.Debug("Session saved successfully")
	}
	return err
}

// SetUserInfo stores user information in the session
// Note: We only store minimal data to avoid cookie size limits
func SetUserInfo(session *sessions.Session, accessToken, refreshToken, username string, expiresAt time.Time) {
	// Generate a unique session key for token storage
	var sessionKey string
	if existingKey, ok := session.Values["session_key"].(string); ok {
		sessionKey = existingKey
	} else {
		sessionKey = generateSessionID()
		session.Values["session_key"] = sessionKey
	}

	// Store only essential data in session to avoid cookie size limits
	// For JWT tokens, we'll store a hash/reference instead of the full token
	session.Values["username"] = username
	session.Values["expires_at"] = expiresAt.Unix()
	session.Values["authenticated"] = true
	session.Values["token_hash"] = hashToken(accessToken)

	// Store tokens in a server-side cache/map (in production, use Redis or similar)
	storeTokens(sessionKey, accessToken, refreshToken)

	slog.Debug("User info stored in session", "username", username, "session_key", sessionKey)
}

// GetUserInfo retrieves user information from the session
func GetUserInfo(session *sessions.Session) (accessToken, refreshToken, username string, expiresAt time.Time, authenticated bool) {
	if session == nil {
		return "", "", "", time.Time{}, false
	}

	if auth, ok := session.Values["authenticated"].(bool); !ok || !auth {
		return "", "", "", time.Time{}, false
	}

	// Get session key for token storage
	sessionKey, ok := session.Values["session_key"].(string)
	if !ok {
		return "", "", "", time.Time{}, false
	}

	// Get tokens from server-side storage
	accessToken, refreshToken, found := getTokens(sessionKey)
	if !found {
		return "", "", "", time.Time{}, false
	}

	username, _ = session.Values["username"].(string)

	if exp, ok := session.Values["expires_at"].(int64); ok {
		expiresAt = time.Unix(exp, 0)
	}

	authenticated = true
	return
}

// ClearUserInfo clears user information from the session
func ClearUserInfo(session *sessions.Session) {
	// Get session key for token storage
	if sessionKey, ok := session.Values["session_key"].(string); ok {
		clearTokens(sessionKey)
	}

	// Clear session data
	delete(session.Values, "username")
	delete(session.Values, "expires_at")
	delete(session.Values, "token_hash")
	delete(session.Values, "session_key")
	session.Values["authenticated"] = false
}

// IsAuthenticated checks if the user is authenticated and the token is not expired
func IsAuthenticated(session *sessions.Session) bool {
	if session == nil {
		return false
	}

	if auth, ok := session.Values["authenticated"].(bool); !ok || !auth {
		return false
	}

	// Check if token is expired
	if exp, ok := session.Values["expires_at"].(int64); ok {
		expiresAt := time.Unix(exp, 0)
		if time.Now().After(expiresAt) {
			return false
		}
	}

	return true
}

// In-memory token storage (in production, use Redis or similar)
var (
	tokenStore = make(map[string]TokenData)
	tokenMutex sync.RWMutex
)

// TokenData holds the token information
type TokenData struct {
	AccessToken  string
	RefreshToken string
	ExpiresAt    time.Time
}

// hashToken creates a hash of the token for verification
func hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// generateSessionID generates a random session ID
func generateSessionID() string {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to timestamp-based ID if random fails
		return fmt.Sprintf("session_%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// storeTokens stores tokens in server-side storage
func storeTokens(sessionKey, accessToken, refreshToken string) {
	tokenMutex.Lock()
	defer tokenMutex.Unlock()

	tokenStore[sessionKey] = TokenData{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    time.Now().Add(1 * time.Hour), // Default expiration
	}

	slog.Debug("Tokens stored", "session_key", sessionKey, "has_access_token", accessToken != "", "has_refresh_token", refreshToken != "")
}

// getTokens retrieves tokens from server-side storage
func getTokens(sessionKey string) (accessToken, refreshToken string, found bool) {
	tokenMutex.RLock()
	defer tokenMutex.RUnlock()

	slog.Debug("Getting tokens from storage", "session_key", sessionKey, "store_size", len(tokenStore))

	// Debug: log all keys in store
	keys := make([]string, 0, len(tokenStore))
	for k := range tokenStore {
		keys = append(keys, k)
	}
	slog.Debug("Available keys in token store", "keys", keys)

	if data, exists := tokenStore[sessionKey]; exists {
		if time.Now().Before(data.ExpiresAt) {
			slog.Debug("Tokens found and valid", "session_key", sessionKey)
			return data.AccessToken, data.RefreshToken, true
		}
		// Token expired, clean up
		slog.Debug("Tokens expired, cleaning up", "session_key", sessionKey)
		delete(tokenStore, sessionKey)
	} else {
		slog.Debug("No tokens found for session", "session_key", sessionKey)
	}
	return "", "", false
}

// clearTokens removes tokens from server-side storage
func clearTokens(sessionKey string) {
	tokenMutex.Lock()
	defer tokenMutex.Unlock()
	delete(tokenStore, sessionKey)
}

// cleanupExpiredTokens removes expired tokens from storage
// This should be called periodically to prevent memory leaks
func cleanupExpiredTokens() {
	tokenMutex.Lock()
	defer tokenMutex.Unlock()

	now := time.Now()
	for sessionID, data := range tokenStore {
		if now.After(data.ExpiresAt) {
			delete(tokenStore, sessionID)
		}
	}
}

// StartTokenCleanup starts a background goroutine to clean up expired tokens
func StartTokenCleanup() {
	go func() {
		ticker := time.NewTicker(15 * time.Minute) // Clean up every 15 minutes
		defer ticker.Stop()

		for range ticker.C {
			cleanupExpiredTokens()
		}
	}()
}
