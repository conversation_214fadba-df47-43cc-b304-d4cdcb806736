// filepath: /Users/<USER>/Developer/github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware/keycloak.go
package middleware

import (
	"context"
	"crypto/rsa"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/Nerzal/gocloak/v13"
	"github.com/golang-jwt/jwt/v5"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
)

type contextKey string

const (
	// TokenContextKey is the key used to store the Keycloak token in the request context
	TokenContextKey contextKey = "token"
	// UserContextKey is the key used to store the user info in the request context
	UserContextKey contextKey = "user"
)

// KeycloakConfig holds the configuration for Keycloak middleware
type KeycloakConfig struct {
	URL          string
	Realm        string
	ClientID     string
	ClientSecret string
	RequiredRole string
}

// KeycloakClient wraps the gocloak client with caching for public keys
type KeycloakClient struct {
	client         *gocloak.GoCloak
	config         *KeycloakConfig
	publicKey      *rsa.PublicKey
	keyMutex       sync.RWMutex
	keyExpiry      time.Time
	sessionService *services.UserSessionService // For managing user sessions
}

// NewKeycloakClient creates a new Keycloak client
func NewKeycloakClient(config *KeycloakConfig, sessionService *services.UserSessionService) *KeycloakClient {
	client := gocloak.NewClient(config.URL)
	return &KeycloakClient{
		client:         client,
		config:         config,
		sessionService: sessionService,
	}
}

// getPublicKey retrieves and caches the Keycloak public key
func (kc *KeycloakClient) getPublicKey(ctx context.Context) (*rsa.PublicKey, error) {
	kc.keyMutex.RLock()
	if kc.publicKey != nil && time.Now().Before(kc.keyExpiry) {
		defer kc.keyMutex.RUnlock()
		return kc.publicKey, nil
	}
	kc.keyMutex.RUnlock()

	kc.keyMutex.Lock()
	defer kc.keyMutex.Unlock()

	// Double-check after acquiring write lock
	if kc.publicKey != nil && time.Now().Before(kc.keyExpiry) {
		return kc.publicKey, nil
	}

	// Get the realm's certificates/keys
	certs, err := kc.client.GetCerts(ctx, kc.config.Realm)
	if err != nil {
		return nil, fmt.Errorf("failed to get realm certificates: %w", err)
	}

	if certs.Keys == nil || len(*certs.Keys) == 0 {
		return nil, fmt.Errorf("no public keys found for realm %s", kc.config.Realm)
	}

	// Find the first RSA key for signing
	var keyData *gocloak.CertResponseKey
	for _, key := range *certs.Keys {
		if key.Kty != nil && *key.Kty == "RSA" && key.Use != nil && *key.Use == "sig" {
			keyData = &key
			break
		}
	}

	if keyData == nil {
		return nil, fmt.Errorf("no RSA signing key found for realm %s", kc.config.Realm)
	}

	// Parse the public key from the certificate
	if keyData.X5c == nil || len(*keyData.X5c) == 0 {
		return nil, fmt.Errorf("no certificate data found in key")
	}

	certPEM := fmt.Sprintf("-----BEGIN CERTIFICATE-----\n%s\n-----END CERTIFICATE-----", (*keyData.X5c)[0])
	publicKey, err := jwt.ParseRSAPublicKeyFromPEM([]byte(certPEM))
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	kc.publicKey = publicKey
	kc.keyExpiry = time.Now().Add(1 * time.Hour) // Cache for 1 hour

	return publicKey, nil
}

// NewKeycloakMiddleware creates a new Keycloak middleware with the provided configuration
func NewKeycloakMiddleware(config *KeycloakConfig, sessionService *services.UserSessionService) func(http.HandlerFunc) http.HandlerFunc {
	keycloakClient := NewKeycloakClient(config, sessionService)

	return func(h http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			var tokenString string
			var username string
			ctx := r.Context()

			// First, try to get token from session (for web browser users)
			session := GetSession(r)
			slog.Debug("Session check", "session_exists", session != nil, "path", r.URL.Path)

			if session != nil {
				isAuth := IsAuthenticated(session)
				slog.Debug("Session authentication check", "is_authenticated", isAuth, "session_id", session.ID)

				if isAuth {
					accessToken, _, sessionUsername, _, _ := GetUserInfo(session)
					slog.Debug("Token retrieval from session", "has_token", accessToken != "", "username", sessionUsername)

					if accessToken != "" {
						tokenString = accessToken
						username = sessionUsername
						slog.Debug("Using token from session", "username", username)
					} else {
						slog.Warn("Session authenticated but no token found", "session_id", session.ID)
					}
				}
			}

			// If no session token, try Authorization header (for API users)
			if tokenString == "" {
				authHeader := r.Header.Get("Authorization")
				if authHeader != "" {
					parts := strings.Split(authHeader, " ")
					if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
						tokenString = parts[1]
						slog.Debug("Using token from Authorization header")
					}
				}
			}

			// If no token found, redirect to login for web requests or return 401 for API requests
			if tokenString == "" {
				if isWebRequest(r) {
					// Redirect to login page
					http.Redirect(w, r, "/login", http.StatusFound)
					return
				} else {
					slog.Warn("Missing Authorization header")
					http.Error(w, "Authorization header is required", http.StatusUnauthorized)
					return
				}
			}

			// Validate the JWT token
			claims, err := validateToken(ctx, keycloakClient, tokenString)
			if err != nil {
				slog.Warn("Token validation failed", "error", err)

				if isWebRequest(r) {
					// Clear session and redirect to login
					if session != nil {
						ClearUserInfo(session)
						SaveSession(r, w, session)
					}
					http.Redirect(w, r, "/login", http.StatusFound)
					return
				} else {
					http.Error(w, "Invalid token", http.StatusUnauthorized)
					return
				}
			}

			// Check for the required role/group
			hasRequiredAccess, err := hasAccess(claims, config.RequiredRole)
			if err != nil || !hasRequiredAccess {
				if username == "" {
					username, _ = claims["preferred_username"].(string)
				}
				slog.Warn("Insufficient permissions", "user", username, "required_role", config.RequiredRole)
				http.Error(w, "Insufficient permissions", http.StatusForbidden)
				return
			}

			// Store token in context for later use
			ctx = context.WithValue(ctx, TokenContextKey, tokenString)

			// If user info is available, store it too
			if username == "" {
				username, _ = claims["preferred_username"].(string)
			}
			if username != "" {
				ctx = context.WithValue(ctx, UserContextKey, username)
				slog.Debug("User authenticated", "username", username)

				// Update user session with accessible companies from JWT claims
				if err := keycloakClient.updateUserSession(username, claims); err != nil {
					slog.Warn("Failed to update user session", "username", username, "error", err)
					// Don't fail the request, just log the warning
				}
			}

			// Process the request with the authenticated context
			h.ServeHTTP(w, r.WithContext(ctx))
		}
	}
}

// isWebRequest determines if this is a web browser request vs API request
func isWebRequest(r *http.Request) bool {
	// Check Accept header for HTML content
	accept := r.Header.Get("Accept")
	return strings.Contains(accept, "text/html") || accept == ""
}

// validateToken validates a JWT token against Keycloak
func validateToken(ctx context.Context, kc *KeycloakClient, tokenString string) (jwt.MapClaims, error) {
	// Get the public key for validation
	publicKey, err := kc.getPublicKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get public key: %w", err)
	}

	// Parse and validate the token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Verify the signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return publicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Check if token is valid
	if !token.Valid {
		return nil, fmt.Errorf("token is not valid")
	}

	// Extract claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("failed to extract claims from token")
	}

	// Verify token is not expired
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			return nil, fmt.Errorf("token is expired")
		}
	}

	// Verify issuer if configured
	if iss, ok := claims["iss"].(string); ok {
		expectedIssuer := fmt.Sprintf("%s/realms/%s", kc.config.URL, kc.config.Realm)
		if iss != expectedIssuer {
			return nil, fmt.Errorf("invalid issuer: expected %s, got %s", expectedIssuer, iss)
		}
	}

	return claims, nil
}

// hasAccess checks if the user has the required role or group
func hasAccess(claims jwt.MapClaims, requiredRole string) (bool, error) {
	// Check in realm roles
	if realmRoles, ok := claims["realm_access"].(map[string]interface{}); ok {
		if roles, ok := realmRoles["roles"].([]interface{}); ok {
			for _, role := range roles {
				if role.(string) == requiredRole {
					return true, nil
				}
			}
		}
	}

	// Check in client roles (resource_access)
	if resourceAccess, ok := claims["resource_access"].(map[string]interface{}); ok {
		for _, clientRoles := range resourceAccess {
			if client, ok := clientRoles.(map[string]interface{}); ok {
				if roles, ok := client["roles"].([]interface{}); ok {
					for _, role := range roles {
						if role.(string) == requiredRole {
							return true, nil
						}
					}
				}
			}
		}
	}

	// Check in groups
	if groups, ok := claims["groups"].([]interface{}); ok {
		for _, group := range groups {
			// Groups can be full paths, so we check if the group name contains the required role
			if strings.Contains(group.(string), requiredRole) {
				return true, nil
			}
		}
	}

	return false, nil
}

// extractAccessibleCompanies extracts accessible company IDs from JWT custom claims
func extractAccessibleCompanies(claims jwt.MapClaims) []int {
	var companyIDs []int

	// Check for custom claim "accessible_companies" or "companies"
	if companies, ok := claims["accessible_companies"]; ok {
		if companyList, ok := companies.([]interface{}); ok {
			for _, company := range companyList {
				if companyID, ok := company.(float64); ok {
					companyIDs = append(companyIDs, int(companyID))
				} else if companyIDStr, ok := company.(string); ok {
					// Try to parse string as int
					if id, err := strconv.Atoi(companyIDStr); err == nil {
						companyIDs = append(companyIDs, id)
					}
				}
			}
		}
	}

	// Alternative: check for "companies" claim
	if len(companyIDs) == 0 {
		if companies, ok := claims["companies"]; ok {
			if companyList, ok := companies.([]interface{}); ok {
				for _, company := range companyList {
					if companyID, ok := company.(float64); ok {
						companyIDs = append(companyIDs, int(companyID))
					} else if companyIDStr, ok := company.(string); ok {
						if id, err := strconv.Atoi(companyIDStr); err == nil {
							companyIDs = append(companyIDs, id)
						}
					}
				}
			}
		}
	}

	// Alternative: check for group-based company access
	// Groups might be in format "/company_1", "/company_2", etc.
	if len(companyIDs) == 0 {
		if groups, ok := claims["groups"]; ok {
			if groupList, ok := groups.([]interface{}); ok {
				for _, group := range groupList {
					if groupStr, ok := group.(string); ok {
						// Extract company ID from group name like "/company_1" or "company_1"
						if strings.HasPrefix(groupStr, "/company_") {
							if id, err := strconv.Atoi(strings.TrimPrefix(groupStr, "/company_")); err == nil {
								companyIDs = append(companyIDs, id)
							}
						} else if strings.HasPrefix(groupStr, "company_") {
							if id, err := strconv.Atoi(strings.TrimPrefix(groupStr, "company_")); err == nil {
								companyIDs = append(companyIDs, id)
							}
						}
					}
				}
			}
		}
	}

	return companyIDs
}

// updateUserSession updates or creates a user session with accessible companies from JWT claims
func (kc *KeycloakClient) updateUserSession(username string, claims jwt.MapClaims) error {
	if kc.sessionService == nil {
		// Session service not available, skip session update
		return nil
	}

	// Extract accessible companies from claims
	accessibleCompanies := extractAccessibleCompanies(claims)

	slog.Debug("Extracted accessible companies from JWT",
		"username", username,
		"companies", accessibleCompanies)

	// Get existing session or create new one
	session, err := kc.sessionService.GetUserSession(username)
	if err != nil {
		return fmt.Errorf("failed to get user session: %w", err)
	}

	if session == nil {
		// Create new session
		session = &database.UserSession{
			Username:            username,
			AccessibleCompanies: accessibleCompanies,
		}

		// Set first accessible company as current if available
		if len(accessibleCompanies) > 0 {
			session.CurrentCompanyID = &accessibleCompanies[0]
		}
	} else {
		// Update existing session with new accessible companies
		session.AccessibleCompanies = accessibleCompanies

		// If current company is not in accessible companies, reset it
		if session.CurrentCompanyID != nil {
			hasAccess := false
			for _, companyID := range accessibleCompanies {
				if companyID == *session.CurrentCompanyID {
					hasAccess = true
					break
				}
			}
			if !hasAccess {
				if len(accessibleCompanies) > 0 {
					session.CurrentCompanyID = &accessibleCompanies[0]
				} else {
					session.CurrentCompanyID = nil
				}
			}
		} else if len(accessibleCompanies) > 0 {
			// Set first accessible company as current if none is set
			session.CurrentCompanyID = &accessibleCompanies[0]
		}
	}

	// Save or update the session
	if err := kc.sessionService.CreateOrUpdateUserSession(session); err != nil {
		return fmt.Errorf("failed to save user session: %w", err)
	}

	slog.Debug("Updated user session",
		"username", username,
		"current_company_id", session.CurrentCompanyID,
		"accessible_companies", session.AccessibleCompanies)

	return nil
}
