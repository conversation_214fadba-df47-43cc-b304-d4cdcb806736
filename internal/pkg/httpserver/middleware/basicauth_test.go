package middleware

import (
	"encoding/base64"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBasicAuthMiddleware_NoAuthHeader(t *testing.T) {
	config := &BasicAuthConfig{
		Username: "admin",
		Password: "secret",
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	middleware := NewBasicAuthMiddleware(config)(handler)
	middleware.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Not authorized")
	assert.Equal(t, `Basic realm="Restricted"`, rr.<PERSON>er().Get("WWW-Authenticate"))
}

func TestBasicAuthMiddleware_InvalidAuthHeader(t *testing.T) {
	config := &BasicAuthConfig{
		Username: "admin",
		Password: "secret",
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Bearer token123")
	rr := httptest.NewRecorder()

	middleware := NewBasicAuthMiddleware(config)(handler)
	middleware.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Not authorized")
}

func TestBasicAuthMiddleware_InvalidBase64(t *testing.T) {
	config := &BasicAuthConfig{
		Username: "admin",
		Password: "secret",
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Basic invalid-base64!")
	rr := httptest.NewRecorder()

	middleware := NewBasicAuthMiddleware(config)(handler)
	middleware.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Not authorized")
}

func TestBasicAuthMiddleware_InvalidCredentialsFormat(t *testing.T) {
	config := &BasicAuthConfig{
		Username: "admin",
		Password: "secret",
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Encode credentials without colon separator
	credentials := base64.StdEncoding.EncodeToString([]byte("adminpassword"))
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Basic "+credentials)
	rr := httptest.NewRecorder()

	middleware := NewBasicAuthMiddleware(config)(handler)
	middleware.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Not authorized")
}

func TestBasicAuthMiddleware_WrongCredentials(t *testing.T) {
	config := &BasicAuthConfig{
		Username: "admin",
		Password: "secret",
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Encode wrong credentials
	credentials := base64.StdEncoding.EncodeToString([]byte("admin:wrongpassword"))
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Basic "+credentials)
	rr := httptest.NewRecorder()

	middleware := NewBasicAuthMiddleware(config)(handler)
	middleware.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Not authorized")
}

func TestBasicAuthMiddleware_ValidCredentials(t *testing.T) {
	config := &BasicAuthConfig{
		Username: "admin",
		Password: "secret",
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check that username is stored in context
		if username, ok := r.Context().Value(UserContextKey).(string); ok {
			assert.Equal(t, "admin", username)
		} else {
			t.Error("Username not found in context")
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Encode correct credentials
	credentials := base64.StdEncoding.EncodeToString([]byte("admin:secret"))
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Basic "+credentials)
	rr := httptest.NewRecorder()

	middleware := NewBasicAuthMiddleware(config)(handler)
	middleware.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusOK, rr.Code)
	assert.Equal(t, "success", rr.Body.String())
}

func TestBasicAuthMiddleware_EmptyCredentials(t *testing.T) {
	config := &BasicAuthConfig{
		Username: "admin",
		Password: "secret",
	}

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Encode empty credentials
	credentials := base64.StdEncoding.EncodeToString([]byte(":"))
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Basic "+credentials)
	rr := httptest.NewRecorder()

	middleware := NewBasicAuthMiddleware(config)(handler)
	middleware.ServeHTTP(rr, req)

	assert.Equal(t, http.StatusUnauthorized, rr.Code)
	assert.Contains(t, rr.Body.String(), "Not authorized")
}
