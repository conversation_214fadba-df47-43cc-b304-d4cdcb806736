package middleware

import (
	"context"
	"encoding/base64"
	"log/slog"
	"net/http"
	"strings"
)

// BasicAuthConfig holds the configuration for basic authentication
type BasicAuthConfig struct {
	Username string
	Password string
}

// NewBasicAuthMiddleware creates a new basic authentication middleware with the provided configuration
func NewBasicAuthMiddleware(config *BasicAuthConfig) func(http.HandlerFunc) http.HandlerFunc {
	return func(h http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("WWW-Authenticate", `Basic realm="Restricted"`)

			// Extract Authorization header
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" {
				slog.Warn("Missing Authorization header for basic auth")
				http.Error(w, "Not authorized", http.StatusUnauthorized)
				return
			}

			// Parse Authorization header
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 || strings.ToLower(parts[0]) != "basic" {
				slog.Warn("Invalid Authorization header format for basic auth")
				http.Error(w, "Not authorized", http.StatusUnauthorized)
				return
			}

			// Decode base64 credentials
			credentials, err := base64.StdEncoding.DecodeString(parts[1])
			if err != nil {
				slog.Warn("Failed to decode basic auth credentials", "error", err)
				http.Error(w, "Not authorized", http.StatusUnauthorized)
				return
			}

			// Parse username:password
			pair := strings.SplitN(string(credentials), ":", 2)
			if len(pair) != 2 {
				slog.Warn("Invalid basic auth credentials format")
				http.Error(w, "Not authorized", http.StatusUnauthorized)
				return
			}

			username := pair[0]
			password := pair[1]

			// Validate credentials
			if username != config.Username || password != config.Password {
				slog.Warn("Invalid basic auth credentials", "username", username)
				http.Error(w, "Not authorized", http.StatusUnauthorized)
				return
			}

			// Store username in context for later use
			ctx := context.WithValue(r.Context(), UserContextKey, username)
			slog.Debug("User authenticated via basic auth", "username", username)

			// Process the request with the authenticated context
			h.ServeHTTP(w, r.WithContext(ctx))
		}
	}
}
