package httpserver

import (
	"fmt"
	"log/slog"
	"net/http"
	"time"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/connectionpool"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/handlers"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
	"github.com/probits-as/erv-go-lonn-exporter/public"
)

// Config holds the configuration for the HTTP server
type Config struct {
	Port      int
	BasicAuth *BasicAuthConfig
	Keycloak  *KeycloakConfig
	Session   *SessionConfig
	Database  *DatabaseConfig

	// Multi-tenant components (set during initialization)
	CompanyHandler    *handlers.CompanyHandler
	ContextHandler    *handlers.ContextHandler
	MultiAuthHandler  *handlers.MultiTenantAuthHandler
	ConnectionManager *connectionpool.ConnectionManager
	SessionService    *services.UserSessionService
}

// DatabaseConfig holds the database configuration
type DatabaseConfig struct {
	Path         string
	MaxOpenConns int
	MaxIdleConns int
}

// BasicAuthConfig holds the basic authentication configuration
type BasicAuthConfig struct {
	Username string
	Password string
}

// KeycloakConfig holds the Keycloak authentication configuration
type KeycloakConfig struct {
	Enabled      bool
	URL          string
	Realm        string
	ClientID     string
	ClientSecret string
	RequiredRole string
	RedirectURL  string
}

// SessionConfig holds the session configuration
type SessionConfig struct {
	SecretKey string
	MaxAge    int
	Secure    bool
	HttpOnly  bool
}

func use(h http.HandlerFunc, middleware ...func(http.HandlerFunc) http.HandlerFunc) http.HandlerFunc {
	for _, m := range middleware {
		h = m(h)
	}
	return h
}

// Start starts a http-server with the provided configuration
func Start(config *Config) error {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		return fmt.Errorf("failed to get embedded assets: %w", err)
	}

	// Use embedded filesystem for static files
	fs := http.FileServer(http.FS(staticFS))
	http.Handle("/public/", http.StripPrefix("/public/", fs))

	// Setup session store for authentication
	var sessionStore *middleware.SessionStore
	var sessionMiddleware func(http.HandlerFunc) http.HandlerFunc

	// Create session configuration (needed for both Keycloak and email-based auth)
	sessionConfig := &middleware.SessionConfig{
		MaxAge:   3600, // 1 hour
		HttpOnly: true,
		Secure:   false, // Set to true in production with HTTPS
		SameSite: http.SameSiteLaxMode,
	}

	if config.Session != nil {
		sessionConfig.SecretKey = config.Session.SecretKey
		if config.Session.MaxAge > 0 {
			sessionConfig.MaxAge = config.Session.MaxAge
		}
		sessionConfig.Secure = config.Session.Secure
		sessionConfig.HttpOnly = config.Session.HttpOnly
	}

	sessionStore = middleware.NewSessionStore(sessionConfig)
	sessionMiddleware = middleware.NewSessionMiddleware(sessionStore)

	if config.Keycloak != nil && config.Keycloak.Enabled {
		// Start token cleanup for server-side token storage
		middleware.StartTokenCleanup()
	}

	// Initialize database if configured
	var settingsHandler *handlers.SettingsHandler
	var repo database.Repository
	if config.Database != nil {
		// Setup SQLite database
		dbConfig := &database.SQLiteConfig{
			DatabasePath: config.Database.Path,
			MaxOpenConns: config.Database.MaxOpenConns,
			MaxIdleConns: config.Database.MaxIdleConns,
		}

		sqliteDB, err := database.NewSQLiteDB(dbConfig)
		if err != nil {
			return fmt.Errorf("failed to initialize database: %w", err)
		}

		// Create repository and services
		repo = database.NewSQLiteRepository(sqliteDB)
		settingsService := services.NewSettingsService(repo)
		settingsHandler = handlers.NewSettingsHandler(settingsService)

		// Create multi-tenant services
		companyService := services.NewCompanyService(repo)
		connectionService := services.NewDatabaseConnectionService(repo)
		sessionService := services.NewUserSessionService(repo)

		// Create connection manager
		connectionManager := connectionpool.NewConnectionManager(companyService, connectionService, sessionService)

		// Create multi-tenant handlers
		companyHandler := handlers.NewCompanyHandler(companyService)
		contextHandler := handlers.NewContextHandler(connectionManager, companyService)
		multiAuthHandler := handlers.NewMultiTenantAuthHandler(companyService)

		// Store handlers and services for use in route setup
		config.CompanyHandler = companyHandler
		config.ContextHandler = contextHandler
		config.MultiAuthHandler = multiAuthHandler
		config.ConnectionManager = connectionManager
		config.SessionService = sessionService

		// Start periodic cleanup of expired sessions
		go func() {
			ticker := time.NewTicker(1 * time.Hour)
			defer ticker.Stop()
			for range ticker.C {
				if err := repo.CleanupExpiredSessions(); err != nil {
					slog.Error("Failed to cleanup expired sessions", "error", err)
				}
			}
		}()

		slog.Info("Database initialized successfully", "path", config.Database.Path)
	}

	// Determine which auth middleware to use based on configuration
	var authMiddleware func(http.HandlerFunc) http.HandlerFunc
	if config.Keycloak != nil && config.Keycloak.Enabled {
		slog.Debug("Keycloak is enabled, using keycloak auth middleware",
			"url", config.Keycloak.URL, "realm", config.Keycloak.Realm,
			"client_id", config.Keycloak.ClientID, "required_role", config.Keycloak.RequiredRole)

		keycloakConfig := &middleware.KeycloakConfig{
			URL:          config.Keycloak.URL,
			Realm:        config.Keycloak.Realm,
			ClientID:     config.Keycloak.ClientID,
			ClientSecret: config.Keycloak.ClientSecret,
			RequiredRole: config.Keycloak.RequiredRole,
		}

		// Setup auth handlers
		authHandlerConfig := &handlers.AuthConfig{
			KeycloakURL:    config.Keycloak.URL,
			Realm:          config.Keycloak.Realm,
			ClientID:       config.Keycloak.ClientID,
			ClientSecret:   config.Keycloak.ClientSecret,
			RequiredRole:   config.Keycloak.RequiredRole,
			RedirectURL:    config.Keycloak.RedirectURL,
			SessionStore:   sessionStore,
			KeycloakClient: middleware.NewKeycloakClient(keycloakConfig, config.SessionService),
		}
		handlers.SetAuthConfig(authHandlerConfig)

		authMiddleware = middleware.NewKeycloakMiddleware(keycloakConfig, config.SessionService)
	} else if config.BasicAuth != nil {
		slog.Debug("Keycloak is not enabled, using basic auth middleware",
			"username", config.BasicAuth.Username, "password", config.BasicAuth.Password)
		basicAuthConfig := &middleware.BasicAuthConfig{
			Username: config.BasicAuth.Username,
			Password: config.BasicAuth.Password,
		}
		authMiddleware = middleware.NewBasicAuthMiddleware(basicAuthConfig)
	} else if repo != nil {
		// Email-based authentication - use session-based auth middleware
		slog.Info("Using email-based authentication with session middleware")
		authMiddleware = func(h http.HandlerFunc) http.HandlerFunc {
			return func(w http.ResponseWriter, r *http.Request) {
				// For email-based auth, we'll implement session checking here
				// For now, allow all requests (this should be enhanced with proper session validation)
				h.ServeHTTP(w, r)
			}
		}
	} else {
		// Fallback to no authentication (not recommended for production)
		slog.Warn("No authentication configured, this is not recommended for production")
		authMiddleware = func(h http.HandlerFunc) http.HandlerFunc {
			return h
		}
	}

	// Setup routes based on authentication type
	if config.BasicAuth != nil {
		// Basic authentication mode
		slog.Info("Setting up basic authentication routes")

		// Main page route for basic auth
		http.HandleFunc("/", use(handlers.ServeTemplates, authMiddleware))
		http.HandleFunc("/login", use(handlers.ServeLoginPage))

	} else if (config.Keycloak != nil && config.Keycloak.Enabled) || repo != nil {
		// Email-based authentication routes (no auth middleware needed)
		slog.Info("Setting up email-based authentication routes")

		var emailAuthHandler *handlers.EmailAuthHandler
		if repo != nil {
			emailAuthHandler = handlers.NewEmailAuthHandler(repo)
		} else {
			// Create a nil repository handler for backward compatibility
			emailAuthHandler = handlers.NewEmailAuthHandler(nil)
		}
		http.HandleFunc("/auth/login", emailAuthHandler.HandleEmailLogin)
		http.HandleFunc("/auth/callback", emailAuthHandler.HandleCallback)
		http.HandleFunc("/auth/logout", use(handlers.Logout, sessionMiddleware))
		http.HandleFunc("/login", use(handlers.ServeLoginPage))

		// Email login page at root (no auth required)
		http.HandleFunc("/", emailAuthHandler.ServeEmailLoginPage)

	} else {
		// No authentication configured - show error
		slog.Error("No authentication method configured. Use --auth-basic for basic auth or configure database for email auth.")
		return fmt.Errorf("no authentication method configured")
	}

	// Common protected routes (regardless of auth method)
	// Context management API (protected)
	if config.ContextHandler != nil && config.ConnectionManager != nil {
		connectionMiddleware := connectionpool.ConnectionMiddleware(config.ConnectionManager)
		http.HandleFunc("/api/context", use(config.ContextHandler.GetUserContext, authMiddleware, sessionMiddleware, connectionMiddleware))
		http.HandleFunc("/api/context/switch", use(config.ContextHandler.SwitchCompany, authMiddleware, sessionMiddleware, connectionMiddleware))
		http.HandleFunc("/api/context/databases", use(config.ContextHandler.GetCompanyDatabases, authMiddleware, sessionMiddleware, connectionMiddleware))
	}

	// Protected application routes (with session, auth, and connection middleware)
	// Note: middleware is applied right-to-left
	var connectionMiddleware func(http.HandlerFunc) http.HandlerFunc
	if config.ConnectionManager != nil {
		connectionMiddleware = connectionpool.ConnectionMiddleware(config.ConnectionManager)
		http.HandleFunc("/app", use(handlers.ServeTemplates, authMiddleware, sessionMiddleware, connectionMiddleware))
		http.HandleFunc("/employees", use(handlers.Employees, authMiddleware, sessionMiddleware, connectionMiddleware))
		http.HandleFunc("/export", use(handlers.Export, authMiddleware, sessionMiddleware, connectionMiddleware))
		http.HandleFunc("/download", use(handlers.Download, authMiddleware, sessionMiddleware, connectionMiddleware))
		http.HandleFunc("/report", use(handlers.Report, authMiddleware, sessionMiddleware, connectionMiddleware))
	} else {
		// Fallback without connection middleware
		http.HandleFunc("/app", use(handlers.ServeTemplates, authMiddleware, sessionMiddleware))
		http.HandleFunc("/employees", use(handlers.Employees, authMiddleware, sessionMiddleware))
		http.HandleFunc("/export", use(handlers.Export, authMiddleware, sessionMiddleware))
		http.HandleFunc("/download", use(handlers.Download, authMiddleware, sessionMiddleware))
		http.HandleFunc("/report", use(handlers.Report, authMiddleware, sessionMiddleware))
	}

	// Settings API routes (protected)
	if settingsHandler != nil {
		http.HandleFunc("/api/settings/form-data", use(func(w http.ResponseWriter, r *http.Request) {
			switch r.Method {
			case http.MethodGet:
				settingsHandler.GetFormData(w, r)
			case http.MethodPost:
				settingsHandler.SaveFormData(w, r)
			case http.MethodDelete:
				settingsHandler.DeleteFormData(w, r)
			default:
				http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			}
		}, authMiddleware, sessionMiddleware))

		// Generic settings endpoints
		http.HandleFunc("/api/settings/", use(func(w http.ResponseWriter, r *http.Request) {
			switch r.Method {
			case http.MethodGet:
				settingsHandler.GetUserSetting(w, r)
			case http.MethodPost:
				settingsHandler.SetUserSetting(w, r)
			case http.MethodDelete:
				settingsHandler.DeleteUserSetting(w, r)
			default:
				http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			}
		}, authMiddleware, sessionMiddleware))
	}

	return http.ListenAndServe(fmt.Sprintf(":%d", config.Port), nil)
}
