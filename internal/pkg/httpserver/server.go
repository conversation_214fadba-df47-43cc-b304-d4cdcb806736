package httpserver

import (
	"fmt"
	"log/slog"
	"net/http"
	"time"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/handlers"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
	"github.com/probits-as/erv-go-lonn-exporter/public"
)

// Config holds the configuration for the HTTP server
type Config struct {
	Port      int
	BasicAuth *BasicAuthConfig
	Keycloak  *KeycloakConfig
	Session   *SessionConfig
	Database  *DatabaseConfig
}

// DatabaseConfig holds the database configuration
type DatabaseConfig struct {
	Path         string
	MaxOpenConns int
	MaxIdleConns int
}

// BasicAuthConfig holds the basic authentication configuration
type BasicAuthConfig struct {
	Username string
	Password string
}

// KeycloakConfig holds the Keycloak authentication configuration
type KeycloakConfig struct {
	Enabled      bool
	URL          string
	Realm        string
	ClientID     string
	ClientSecret string
	RequiredRole string
	RedirectURL  string
}

// SessionConfig holds the session configuration
type SessionConfig struct {
	SecretKey string
	MaxAge    int
	Secure    bool
	HttpOnly  bool
}

func use(h http.HandlerFunc, middleware ...func(http.HandlerFunc) http.HandlerFunc) http.HandlerFunc {
	for _, m := range middleware {
		h = m(h)
	}
	return h
}

// Start starts a http-server with the provided configuration
func Start(config *Config) error {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		return fmt.Errorf("failed to get embedded assets: %w", err)
	}

	// Use embedded filesystem for static files
	fs := http.FileServer(http.FS(staticFS))
	http.Handle("/public/", http.StripPrefix("/public/", fs))

	// Setup session store if Keycloak is enabled
	var sessionStore *middleware.SessionStore
	var sessionMiddleware func(http.HandlerFunc) http.HandlerFunc

	if config.Keycloak != nil && config.Keycloak.Enabled {
		// Create session configuration
		sessionConfig := &middleware.SessionConfig{
			MaxAge:   3600, // 1 hour
			HttpOnly: true,
			Secure:   false, // Set to true in production with HTTPS
			SameSite: http.SameSiteLaxMode,
		}

		if config.Session != nil {
			sessionConfig.SecretKey = config.Session.SecretKey
			if config.Session.MaxAge > 0 {
				sessionConfig.MaxAge = config.Session.MaxAge
			}
			sessionConfig.Secure = config.Session.Secure
			sessionConfig.HttpOnly = config.Session.HttpOnly
		}

		sessionStore = middleware.NewSessionStore(sessionConfig)
		sessionMiddleware = middleware.NewSessionMiddleware(sessionStore)

		// Start token cleanup for server-side token storage
		middleware.StartTokenCleanup()
	}

	// Initialize database if configured
	var settingsHandler *handlers.SettingsHandler
	if config.Database != nil {
		// Setup SQLite database
		dbConfig := &database.SQLiteConfig{
			DatabasePath: config.Database.Path,
			MaxOpenConns: config.Database.MaxOpenConns,
			MaxIdleConns: config.Database.MaxIdleConns,
		}

		sqliteDB, err := database.NewSQLiteDB(dbConfig)
		if err != nil {
			return fmt.Errorf("failed to initialize database: %w", err)
		}

		// Create repository and services
		repo := database.NewSQLiteRepository(sqliteDB)
		settingsService := services.NewSettingsService(repo)
		settingsHandler = handlers.NewSettingsHandler(settingsService)

		// Start periodic cleanup of expired sessions
		go func() {
			ticker := time.NewTicker(1 * time.Hour)
			defer ticker.Stop()
			for range ticker.C {
				if err := repo.CleanupExpiredSessions(); err != nil {
					slog.Error("Failed to cleanup expired sessions", "error", err)
				}
			}
		}()

		slog.Info("Database initialized successfully", "path", config.Database.Path)
	}

	// Determine which auth middleware to use based on configuration
	var authMiddleware func(http.HandlerFunc) http.HandlerFunc
	if config.Keycloak != nil && config.Keycloak.Enabled {
		slog.Debug("Keycloak is enabled, using keycloak auth middleware",
			"url", config.Keycloak.URL, "realm", config.Keycloak.Realm,
			"client_id", config.Keycloak.ClientID, "required_role", config.Keycloak.RequiredRole)

		keycloakConfig := &middleware.KeycloakConfig{
			URL:          config.Keycloak.URL,
			Realm:        config.Keycloak.Realm,
			ClientID:     config.Keycloak.ClientID,
			ClientSecret: config.Keycloak.ClientSecret,
			RequiredRole: config.Keycloak.RequiredRole,
		}

		// Setup auth handlers
		authHandlerConfig := &handlers.AuthConfig{
			KeycloakURL:    config.Keycloak.URL,
			Realm:          config.Keycloak.Realm,
			ClientID:       config.Keycloak.ClientID,
			ClientSecret:   config.Keycloak.ClientSecret,
			RequiredRole:   config.Keycloak.RequiredRole,
			RedirectURL:    config.Keycloak.RedirectURL,
			SessionStore:   sessionStore,
			KeycloakClient: middleware.NewKeycloakClient(keycloakConfig),
		}
		handlers.SetAuthConfig(authHandlerConfig)

		authMiddleware = middleware.NewKeycloakMiddleware(keycloakConfig)
	} else if config.BasicAuth != nil {
		slog.Debug("Keycloak is not enabled, using basic auth middleware",
			"username", config.BasicAuth.Username, "password", config.BasicAuth.Password)
		basicAuthConfig := &middleware.BasicAuthConfig{
			Username: config.BasicAuth.Username,
			Password: config.BasicAuth.Password,
		}
		authMiddleware = middleware.NewBasicAuthMiddleware(basicAuthConfig)
	} else {
		// Fallback to no authentication (not recommended for production)
		slog.Warn("No authentication configured, this is not recommended for production")
		authMiddleware = func(h http.HandlerFunc) http.HandlerFunc {
			return h
		}
	}

	// Setup routes based on authentication type
	if config.Keycloak != nil && config.Keycloak.Enabled {
		// Authentication routes (no auth middleware needed)
		http.HandleFunc("/auth/login", use(handlers.Login, sessionMiddleware))
		http.HandleFunc("/auth/callback", use(handlers.Callback, sessionMiddleware))
		http.HandleFunc("/auth/logout", use(handlers.Logout, sessionMiddleware))
		http.HandleFunc("/login", use(handlers.ServeLoginPage))

		// Protected routes (with session and auth middleware)
		// Note: middleware is applied right-to-left, so authMiddleware runs first, then sessionMiddleware
		http.HandleFunc("/", use(handlers.ServeTemplates, authMiddleware, sessionMiddleware))
		http.HandleFunc("/employees", use(handlers.Employees, authMiddleware, sessionMiddleware))
		http.HandleFunc("/export", use(handlers.Export, authMiddleware, sessionMiddleware))
		http.HandleFunc("/download", use(handlers.Download, authMiddleware, sessionMiddleware))
		http.HandleFunc("/report", use(handlers.Report, authMiddleware, sessionMiddleware))

		// Settings API routes (protected)
		if settingsHandler != nil {
			http.HandleFunc("/api/settings/form-data", use(func(w http.ResponseWriter, r *http.Request) {
				switch r.Method {
				case http.MethodGet:
					settingsHandler.GetFormData(w, r)
				case http.MethodPost:
					settingsHandler.SaveFormData(w, r)
				case http.MethodDelete:
					settingsHandler.DeleteFormData(w, r)
				default:
					http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
				}
			}, authMiddleware, sessionMiddleware))

			// Generic settings endpoints
			http.HandleFunc("/api/settings/", use(func(w http.ResponseWriter, r *http.Request) {
				switch r.Method {
				case http.MethodGet:
					settingsHandler.GetUserSetting(w, r)
				case http.MethodPost:
					settingsHandler.SetUserSetting(w, r)
				case http.MethodDelete:
					settingsHandler.DeleteUserSetting(w, r)
				default:
					http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
				}
			}, authMiddleware, sessionMiddleware))
		}
	} else {
		// Basic auth or no auth routes
		http.HandleFunc("/", use(handlers.ServeTemplates, authMiddleware))
		http.HandleFunc("/employees", use(handlers.Employees, authMiddleware))
		http.HandleFunc("/export", use(handlers.Export, authMiddleware))
		http.HandleFunc("/download", use(handlers.Download, authMiddleware))
		http.HandleFunc("/report", use(handlers.Report, authMiddleware))
	}

	return http.ListenAndServe(fmt.Sprintf(":%d", config.Port), nil)
}
