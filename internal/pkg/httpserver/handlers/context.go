package handlers

import (
	"encoding/json"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/connectionpool"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
)

// ContextHandler handles user context and company switching requests
type ContextHandler struct {
	connectionManager *connectionpool.ConnectionManager
	companyService    *services.CompanyService
}

// NewContextHandler creates a new context handler
func NewContextHandler(connectionManager *connectionpool.ConnectionManager, companyService *services.CompanyService) *ContextHandler {
	return &ContextHandler{
		connectionManager: connectionManager,
		companyService:    companyService,
	}
}

// GetUserContext handles GET /api/context - returns user's current context
func (h *ContextHandler) GetUserContext(w http.ResponseWriter, r *http.Request) {
	userCtx, err := connectionpool.GetUserContextFromRequest(r, h.connectionManager)
	if err != nil {
		slog.Error("Failed to get user context", "error", err)
		http.Error(w, "Failed to get user context", http.StatusUnauthorized)
		return
	}

	// Get accessible companies with details
	accessibleCompanyIDs, err := userCtx.GetAccessibleCompanies()
	if err != nil {
		slog.Error("Failed to get accessible companies", "error", err, "username", userCtx.Username)
		http.Error(w, "Failed to get accessible companies", http.StatusInternalServerError)
		return
	}

	// Fetch company details
	var accessibleCompanies []interface{}
	for _, companyID := range accessibleCompanyIDs {
		company, err := h.companyService.GetCompany(companyID)
		if err != nil {
			slog.Warn("Failed to get company details", "company_id", companyID, "error", err)
			continue
		}
		if company != nil {
			// Don't expose sensitive information like client secrets
			accessibleCompanies = append(accessibleCompanies, map[string]interface{}{
				"id":           company.ID,
				"name":         company.Name,
				"display_name": company.DisplayName,
				"enabled":      company.Enabled,
			})
		}
	}

	// Get current company details
	var currentCompany interface{}
	if userCtx.CompanyID != nil {
		company, err := h.companyService.GetCompany(*userCtx.CompanyID)
		if err != nil {
			slog.Warn("Failed to get current company details", "company_id", *userCtx.CompanyID, "error", err)
		} else if company != nil {
			currentCompany = map[string]interface{}{
				"id":           company.ID,
				"name":         company.Name,
				"display_name": company.DisplayName,
				"enabled":      company.Enabled,
			}
		}
	}

	response := map[string]interface{}{
		"username":             userCtx.Username,
		"current_company":      currentCompany,
		"accessible_companies": accessibleCompanies,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		slog.Error("Failed to encode user context response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}

	slog.Debug("Retrieved user context", "username", userCtx.Username, "current_company_id", userCtx.CompanyID)
}

// SwitchCompany handles POST /api/context/switch - switches user's current company
func (h *ContextHandler) SwitchCompany(w http.ResponseWriter, r *http.Request) {
	userCtx, err := connectionpool.GetUserContextFromRequest(r, h.connectionManager)
	if err != nil {
		slog.Error("Failed to get user context", "error", err)
		http.Error(w, "Failed to get user context", http.StatusUnauthorized)
		return
	}

	// Parse request body
	var request struct {
		CompanyID int `json:"company_id"`
	}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		slog.Error("Failed to decode switch company request", "error", err)
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if request.CompanyID <= 0 {
		http.Error(w, "Invalid company ID", http.StatusBadRequest)
		return
	}

	// Switch company
	if err := userCtx.SwitchCompany(request.CompanyID); err != nil {
		slog.Error("Failed to switch company", "error", err, "username", userCtx.Username, "company_id", request.CompanyID)
		http.Error(w, "Failed to switch company", http.StatusForbidden)
		return
	}

	// Get updated company details
	company, err := h.companyService.GetCompany(request.CompanyID)
	if err != nil {
		slog.Warn("Failed to get switched company details", "company_id", request.CompanyID, "error", err)
	}

	var companyDetails interface{}
	if company != nil {
		companyDetails = map[string]interface{}{
			"id":           company.ID,
			"name":         company.Name,
			"display_name": company.DisplayName,
			"enabled":      company.Enabled,
		}
	}

	response := map[string]interface{}{
		"success":         true,
		"message":         "Company switched successfully",
		"current_company": companyDetails,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		slog.Error("Failed to encode switch company response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	slog.Info("User switched company", "username", userCtx.Username, "company_id", request.CompanyID)
}

// GetCompanyDatabases handles GET /api/context/databases?company_id=X - returns database info for a company
func (h *ContextHandler) GetCompanyDatabases(w http.ResponseWriter, r *http.Request) {
	userCtx, err := connectionpool.GetUserContextFromRequest(r, h.connectionManager)
	if err != nil {
		slog.Error("Failed to get user context", "error", err)
		http.Error(w, "Failed to get user context", http.StatusUnauthorized)
		return
	}

	// Get company ID from query parameter
	companyIDStr := r.URL.Query().Get("company_id")
	if companyIDStr == "" {
		http.Error(w, "Company ID is required", http.StatusBadRequest)
		return
	}

	companyID, err := strconv.Atoi(companyIDStr)
	if err != nil || companyID <= 0 {
		http.Error(w, "Invalid company ID", http.StatusBadRequest)
		return
	}

	// Check if user has access to this company
	hasAccess, err := userCtx.HasCompanyAccess(companyID)
	if err != nil {
		slog.Error("Failed to check company access", "error", err, "username", userCtx.Username, "company_id", companyID)
		http.Error(w, "Failed to check access", http.StatusInternalServerError)
		return
	}

	if !hasAccess {
		slog.Warn("User attempted to access unauthorized company", "username", userCtx.Username, "company_id", companyID)
		http.Error(w, "Access denied", http.StatusForbidden)
		return
	}

	// Get company details
	company, err := h.companyService.GetCompany(companyID)
	if err != nil {
		slog.Error("Failed to get company", "error", err, "company_id", companyID)
		http.Error(w, "Failed to get company", http.StatusInternalServerError)
		return
	}

	if company == nil {
		http.Error(w, "Company not found", http.StatusNotFound)
		return
	}

	// Test database connection
	dbConnection, err := h.connectionManager.GetConnectionForCompany(companyID)
	var connectionStatus string
	var connectionError string

	if err != nil {
		connectionStatus = "error"
		connectionError = err.Error()
		slog.Warn("Database connection test failed", "company_id", companyID, "error", err)
	} else if dbConnection != nil {
		connectionStatus = "connected"
		slog.Debug("Database connection test successful", "company_id", companyID)
	} else {
		connectionStatus = "not_configured"
	}

	response := map[string]interface{}{
		"company": map[string]interface{}{
			"id":           company.ID,
			"name":         company.Name,
			"display_name": company.DisplayName,
			"enabled":      company.Enabled,
		},
		"database_connection": map[string]interface{}{
			"status": connectionStatus,
			"error":  connectionError,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		slog.Error("Failed to encode company databases response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	slog.Debug("Retrieved company database info", "username", userCtx.Username, "company_id", companyID)
}
