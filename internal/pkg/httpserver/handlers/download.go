// filepath: /Users/<USER>/Developer/github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/handlers/download.go
package handlers

import (
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/exporter"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
	"github.com/spf13/viper"
)

// Download http-handler for downloading export files
func Download(w http.ResponseWriter, r *http.Request) {
	r.ParseForm()
	slog.Debug("handling download request",
		"type", "HTTP-handler",
		"method", r.Method,
		"url", r.RequestURI,
		"form", r.PostForm,
	)

	var dateFrom string
	if r.PostFormValue("fromDate") != "" {
		sDateFrom := strings.Split(r.PostFormValue("fromDate"), "-")
		dateFrom = fmt.Sprintf("%s%s%s", sDateFrom[2], sDateFrom[1], sDateFrom[0])
	}

	var dateTo string
	if r.PostFormValue("fromDate") != "" {
		sDateTo := strings.Split(r.PostFormValue("toDate"), "-")
		dateTo = fmt.Sprintf("%s%s%s", sDateTo[2], sDateTo[1], sDateTo[0])
	}

	employees := r.PostFormValue("employees")
	salaries := r.PostFormValue("salaries")
	excludedSalaries := r.PostFormValue("excludedSalaries")

	var showExported bool
	if r.PostFormValue("showExported") == "on" {
		showExported = true
	} else {
		showExported = false
	}

	var showHourSalaries bool
	if r.PostFormValue("showHourSalaries") == "on" {
		showHourSalaries = true
	} else {
		showHourSalaries = false
	}

	var showMonthlySalaries bool
	if r.PostFormValue("showMonthlySalaries") == "on" {
		showMonthlySalaries = true
	} else {
		showMonthlySalaries = false
	}

	// Get the export format (defaulting to VLSTANDARD if not specified)
	exportFormat := r.PostFormValue("exportFormat")
	var exportType exporter.VismaExportType
	switch exportFormat {
	default:
		exportType = exporter.VLSTANDARD
	}

	dbClient, ok := viper.Get("visma.client").(*vismadb.Client)
	if !ok {
		http.Error(w, http.StatusText(500), 500)
		slog.Error("failed to get visma client",
			"type", "HTTP-handler",
			"method", r.Method,
			"url", r.RequestURI,
		)
		return
	}

	if dateFrom != "" && dateTo != "" {
		var hourSalaries *[]vismadb.OrdLn
		var hourSalariesIDs map[int][]int
		var monthlySalaries *[]vismadb.OrdLn
		var monthlySalariesIDs map[int][]int
		var err error

		if showHourSalaries {
			hourSalaries, hourSalariesIDs, err = dbClient.GetHourSalaries(employees, dateFrom, dateTo, showExported)
			if err != nil {
				http.Error(w, http.StatusText(500), 500)
				slog.Error("error getting hour salaries",
					"type", "HTTP-handler",
					"method", r.Method,
					"url", r.RequestURI,
					"error", err,
				)
				return
			}
		}

		if showMonthlySalaries {
			monthlySalaries, monthlySalariesIDs, err = dbClient.GetMonthlySalaries(employees, salaries, excludedSalaries, dateFrom, dateTo, showExported)
			if err != nil {
				http.Error(w, http.StatusText(500), 500)
				slog.Error("error getting monthly salaries",
					"type", "HTTP-handler",
					"method", r.Method,
					"url", r.RequestURI,
					"error", err,
				)
				return
			}
		}

		// Combine hourSalaries and monthlySalaries
		var orderlines []vismadb.OrdLn
		if hourSalaries != nil {
			orderlines = append(orderlines, *hourSalaries...)
		}
		if monthlySalaries != nil {
			orderlines = append(orderlines, *monthlySalaries...)
		}

		// Combine hourSalariesIDs and monthlySalariesIDs
		orderIDs := make(map[int][]int)
		for k, v := range hourSalariesIDs {
			orderIDs[k] = v
		}
		for k, v := range monthlySalariesIDs {
			orderIDs[k] = v
		}

		// Create a temporary file for download
		tempDir := os.TempDir()
		timestamp := time.Now().Format("20060102_150405")
		filename := fmt.Sprintf("erv_lonn_%s.txt", timestamp)
		filePath := filepath.Join(tempDir, filename)

		// Create exporter with the temporary file path and specified export type
		exporter := exporter.NewVismaExportConfig(exportType, filePath)

		// Export to the temporary file
		if err := exporter.ExportOrderLinesToFile(&orderlines); err != nil {
			http.Error(w, http.StatusText(500), 500)
			slog.Error("error exporting order lines to file",
				"type", "HTTP-handler",
				"method", r.Method,
				"url", r.RequestURI,
				"error", err,
			)
			return
		}

		free4, err := dbClient.GetNewFree4()
		if err != nil {
			http.Error(w, http.StatusText(500), 500)
			slog.Error("error getting new free4",
				"type", "HTTP-handler",
				"method", r.Method,
				"url", r.RequestURI,
				"error", err,
			)
			return
		}

		_, err = dbClient.UpdateFree4(free4, orderIDs)
		if err != nil {
			http.Error(w, http.StatusText(500), 500)
			slog.Error("error updating free4",
				"type", "HTTP-handler",
				"method", r.Method,
				"url", r.RequestURI,
				"error", err,
			)
			return
		}

		// Serve the file for download
		w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
		w.Header().Set("Content-Type", "text/plain")
		http.ServeFile(w, r, filePath)

		// Schedule file cleanup after a delay
		go func() {
			time.Sleep(5 * time.Minute)
			os.Remove(filePath)
		}()

	} else {
		http.Error(w, http.StatusText(400), 400)
		slog.Error("dates must be set",
			"type", "HTTP-handler",
			"method", r.Method,
			"url", r.RequestURI,
		)
		return
	}
}
