package handlers

import (
	"encoding/json"
	"html/template"
	"io/fs"
	"log/slog"
	"net/http"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
	"github.com/probits-as/erv-go-lonn-exporter/public"
	"github.com/spf13/viper"
)

// CompanyHandler handles company-related HTTP requests
type CompanyHandler struct {
	companyService *services.CompanyService
}

// NewCompanyHandler creates a new company handler
func NewCompanyHandler(companyService *services.CompanyService) *CompanyHandler {
	return &CompanyHandler{
		companyService: companyService,
	}
}

// ServeCompanySelectionPage serves the company selection landing page
func (h *CompanyHandler) ServeCompanySelectionPage(w http.ResponseWriter, r *http.Request) {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		slog.Error("error getting embedded assets", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Load company selection template
	templateFile, err := fs.ReadFile(staticFS, "templates/company-selection.html")
	if err != nil {
		slog.Error("error reading company selection template", "error", err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Load layout template
	layoutFile, err := fs.ReadFile(staticFS, "templates/layout.html")
	if err != nil {
		slog.Error("error reading layout template", "error", err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Parse templates
	tmpl, err := template.New("layout").Parse(string(layoutFile))
	if err != nil {
		slog.Error("error parsing layout template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	_, err = tmpl.New("company-selection").Parse(string(templateFile))
	if err != nil {
		slog.Error("error parsing company selection template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Get companies from service
	companies, err := h.companyService.GetCompanies()
	if err != nil {
		slog.Error("error getting companies", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Prepare template data
	data := struct {
		Version   string
		Companies interface{}
	}{
		Version:   viper.GetString("version"),
		Companies: companies,
	}

	// Execute template
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	if err := tmpl.ExecuteTemplate(w, "layout", data); err != nil {
		slog.Error("error executing company selection template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

// GetCompaniesAPI returns companies as JSON for API requests
func (h *CompanyHandler) GetCompaniesAPI(w http.ResponseWriter, r *http.Request) {
	companies, err := h.companyService.GetCompanies()
	if err != nil {
		slog.Error("error getting companies", "error", err)
		http.Error(w, "Failed to get companies", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(companies); err != nil {
		slog.Error("error writing JSON response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}

// GetCompanyAPI returns a specific company as JSON
func (h *CompanyHandler) GetCompanyAPI(w http.ResponseWriter, r *http.Request) {
	// Extract company ID from URL path or query parameter
	// This is a simplified implementation - in a real app you'd use a router like gorilla/mux
	companyName := r.URL.Query().Get("name")
	if companyName == "" {
		http.Error(w, "Company name is required", http.StatusBadRequest)
		return
	}

	company, err := h.companyService.GetCompanyByName(companyName)
	if err != nil {
		slog.Error("error getting company", "error", err, "name", companyName)
		http.Error(w, "Failed to get company", http.StatusInternalServerError)
		return
	}

	if company == nil {
		http.Error(w, "Company not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(company); err != nil {
		slog.Error("error writing JSON response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}
