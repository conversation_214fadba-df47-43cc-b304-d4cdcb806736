package handlers

import (
	"html/template"
	"io/fs"
	"log/slog"
	"net/http"
	"strings"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/public"
	"github.com/spf13/viper"
)

// EmailAuthHandler handles email-based authentication routing
type EmailAuthHandler struct {
	repository database.Repository
}

// NewEmailAuthHandler creates a new email authentication handler
func NewEmailAuthHandler(repository database.Repository) *EmailAuthHandler {
	return &EmailAuthHandler{
		repository: repository,
	}
}

// HandleEmailLogin processes email input and redirects to appropriate Keycloak
func (h *EmailAuthHandler) HandleEmailLogin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	email := strings.TrimSpace(r.<PERSON>("email"))
	if email == "" {
		http.Error(w, "Email is required", http.StatusBadRequest)
		return
	}

	slog.Info("Processing email login", "email", email)

	// Get Keycloak configuration for this email
	keycloakConfig := h.getKeycloakConfigForEmail(email)
	if keycloakConfig == nil {
		slog.Warn("No Keycloak configuration found for email", "email", email)
		// Render the login page with error message
		h.ServeEmailLoginPageWithError(w, r, "Ingen innloggingskonfigurasjon funnet for denne e-postadressen. Kontakt systemadministrator.")
		return
	}

	// Set up the auth config for the OAuth flow
	authConfig := &AuthConfig{
		KeycloakURL:  keycloakConfig.URL,
		Realm:        keycloakConfig.Realm,
		ClientID:     keycloakConfig.ClientID,
		ClientSecret: keycloakConfig.ClientSecret,
		RequiredRole: keycloakConfig.RequiredRole,
		RedirectURL:  viper.GetString("server.base_url") + "/auth/callback",
	}
	SetAuthConfig(authConfig)

	// Use the existing Login handler which properly manages OAuth state
	Login(w, r)
}

// KeycloakConfig represents Keycloak configuration
type KeycloakConfig struct {
	URL          string
	Realm        string
	ClientID     string
	ClientSecret string
	RequiredRole string
}

// getKeycloakConfigForEmail determines which Keycloak configuration to use for an email
func (h *EmailAuthHandler) getKeycloakConfigForEmail(email string) *KeycloakConfig {
	// Skip database lookup if no repository is available
	if h.repository == nil {
		// Fall back to config file
		if viper.IsSet("keycloak.url") {
			return &KeycloakConfig{
				URL:          viper.GetString("keycloak.url"),
				Realm:        viper.GetString("keycloak.realm"),
				ClientID:     viper.GetString("keycloak.clientid"),
				ClientSecret: viper.GetString("keycloak.clientsecret"),
				RequiredRole: viper.GetString("keycloak.requiredrole"),
			}
		}
		return nil
	}

	// First, try to find a specific email mapping in the database
	mapping, err := h.repository.GetEmailMappingForEmail(email)
	if err != nil {
		slog.Error("Failed to get email mapping", "error", err, "email", email)
	} else if mapping != nil {
		return &KeycloakConfig{
			URL:          mapping.KeycloakURL,
			Realm:        mapping.KeycloakRealm,
			ClientID:     mapping.KeycloakClientID,
			ClientSecret: mapping.KeycloakClientSecret,
			RequiredRole: mapping.KeycloakRequiredRole,
		}
	}

	// Fall back to default Keycloak configuration from database
	defaultConfig, err := h.repository.GetDefaultKeycloakConfig()
	if err != nil {
		slog.Error("Failed to get default keycloak config", "error", err)
	} else if defaultConfig != nil {
		return &KeycloakConfig{
			URL:          defaultConfig.KeycloakURL,
			Realm:        defaultConfig.KeycloakRealm,
			ClientID:     defaultConfig.KeycloakClientID,
			ClientSecret: defaultConfig.KeycloakClientSecret,
			RequiredRole: defaultConfig.KeycloakRequiredRole,
		}
	}

	// Final fallback to config file (for backward compatibility)
	if viper.IsSet("keycloak.url") {
		return &KeycloakConfig{
			URL:          viper.GetString("keycloak.url"),
			Realm:        viper.GetString("keycloak.realm"),
			ClientID:     viper.GetString("keycloak.clientid"),
			ClientSecret: viper.GetString("keycloak.clientsecret"),
			RequiredRole: viper.GetString("keycloak.requiredrole"),
		}
	}

	return nil
}

// ServeEmailLoginPage serves the email login page
func (h *EmailAuthHandler) ServeEmailLoginPage(w http.ResponseWriter, r *http.Request) {
	// Check if user is already authenticated (we'll implement this check later)
	// For now, just serve the login page
	h.ServeEmailLoginPageWithError(w, r, "")
}

// ServeEmailLoginPageWithError serves the email login page with an optional error message
func (h *EmailAuthHandler) ServeEmailLoginPageWithError(w http.ResponseWriter, r *http.Request, errorMessage string) {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		slog.Error("error getting embedded assets", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Load email login template
	templateFile, err := fs.ReadFile(staticFS, "templates/login.html")
	if err != nil {
		slog.Error("error reading email login template", "error", err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Load layout template
	layoutFile, err := fs.ReadFile(staticFS, "templates/layout.html")
	if err != nil {
		slog.Error("error reading layout template", "error", err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Parse templates
	tmpl, err := template.New("layout").Parse(string(layoutFile))
	if err != nil {
		slog.Error("error parsing layout template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	_, err = tmpl.New("login").Parse(string(templateFile))
	if err != nil {
		slog.Error("error parsing email login template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Get version from configuration
	version := viper.GetString("version")
	if version == "" {
		version = "development"
	}

	data := struct {
		Version      string
		ErrorMessage string
	}{
		Version:      version,
		ErrorMessage: errorMessage,
	}

	// Execute template
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	if err := tmpl.ExecuteTemplate(w, "layout", data); err != nil {
		slog.Error("error executing email login template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

// HandleCallback handles the OAuth callback (delegates to existing callback logic)
func (h *EmailAuthHandler) HandleCallback(w http.ResponseWriter, r *http.Request) {
	// The auth config should already be set from the login handler
	// Just delegate to the existing callback handler
	Callback(w, r)
}
