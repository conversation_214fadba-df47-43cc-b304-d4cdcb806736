package handlers

import (
	"fmt"
	"html/template"
	"io/fs"
	"log/slog"
	"net/http"
	"net/url"
	"strings"

	"github.com/probits-as/erv-go-lonn-exporter/public"
	"github.com/spf13/viper"
)

// EmailAuthHandler handles email-based authentication routing
type EmailAuthHandler struct{}

// NewEmailAuthHandler creates a new email authentication handler
func NewEmailAuthHandler() *EmailAuthHandler {
	return &EmailAuthHandler{}
}

// HandleEmailLogin processes email input and redirects to appropriate Keycloak
func (h *EmailAuthHandler) HandleEmailLogin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	email := strings.TrimSpace(r.FormValue("email"))
	if email == "" {
		http.Error(w, "Email is required", http.StatusBadRequest)
		return
	}

	slog.Info("Processing email login", "email", email)

	// Get Keycloak configuration for this email
	keycloakConfig := h.getKeycloakConfigForEmail(email)
	if keycloakConfig == nil {
		slog.Warn("No Keycloak configuration found for email", "email", email)
		http.Error(w, "No authentication configuration found for this email", http.StatusNotFound)
		return
	}

	// Build authorization URL
	authURL := h.buildAuthorizationURL(keycloakConfig, email)

	slog.Info("Redirecting to Keycloak", "email", email, "realm", keycloakConfig.Realm, "url", authURL)

	// Redirect to Keycloak
	http.Redirect(w, r, authURL, http.StatusFound)
}

// KeycloakConfig represents Keycloak configuration
type KeycloakConfig struct {
	URL          string
	Realm        string
	ClientID     string
	ClientSecret string
	RequiredRole string
}

// getKeycloakConfigForEmail determines which Keycloak configuration to use for an email
func (h *EmailAuthHandler) getKeycloakConfigForEmail(email string) *KeycloakConfig {
	// For now, use a simple domain-based mapping
	// This can be extended to use database mappings later

	domain := h.extractDomain(email)

	// Check for domain-specific configurations
	// Format: keycloak.domains.example.com.url, etc.
	domainConfigKey := fmt.Sprintf("keycloak.domains.%s", strings.ReplaceAll(domain, ".", "_"))

	if viper.IsSet(domainConfigKey + ".url") {
		return &KeycloakConfig{
			URL:          viper.GetString(domainConfigKey + ".url"),
			Realm:        viper.GetString(domainConfigKey + ".realm"),
			ClientID:     viper.GetString(domainConfigKey + ".clientid"),
			ClientSecret: viper.GetString(domainConfigKey + ".clientsecret"),
			RequiredRole: viper.GetString(domainConfigKey + ".requiredrole"),
		}
	}

	// Check for specific email configurations
	// Format: keycloak.emails.user_example_com.url, etc.
	emailConfigKey := fmt.Sprintf("keycloak.emails.%s", strings.ReplaceAll(strings.ReplaceAll(email, "@", "_"), ".", "_"))

	if viper.IsSet(emailConfigKey + ".url") {
		return &KeycloakConfig{
			URL:          viper.GetString(emailConfigKey + ".url"),
			Realm:        viper.GetString(emailConfigKey + ".realm"),
			ClientID:     viper.GetString(emailConfigKey + ".clientid"),
			ClientSecret: viper.GetString(emailConfigKey + ".clientsecret"),
			RequiredRole: viper.GetString(emailConfigKey + ".requiredrole"),
		}
	}

	// Fall back to default Keycloak configuration
	if viper.IsSet("keycloak.url") {
		return &KeycloakConfig{
			URL:          viper.GetString("keycloak.url"),
			Realm:        viper.GetString("keycloak.realm"),
			ClientID:     viper.GetString("keycloak.clientid"),
			ClientSecret: viper.GetString("keycloak.clientsecret"),
			RequiredRole: viper.GetString("keycloak.requiredrole"),
		}
	}

	return nil
}

// extractDomain extracts the domain part from an email address
func (h *EmailAuthHandler) extractDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return ""
	}
	return strings.ToLower(parts[1])
}

// buildAuthorizationURL builds the Keycloak authorization URL
func (h *EmailAuthHandler) buildAuthorizationURL(config *KeycloakConfig, email string) string {
	// Generate state parameter (in production, this should be cryptographically secure)
	state := fmt.Sprintf("email_%s_%d", url.QueryEscape(email), 12345)

	// Get redirect URI from configuration or use default
	redirectURI := viper.GetString("server.base_url")
	if redirectURI == "" {
		redirectURI = "http://localhost:8080"
	}
	redirectURI += "/auth/callback"

	// Build authorization URL
	authURL := fmt.Sprintf("%s/realms/%s/protocol/openid-connect/auth", config.URL, config.Realm)

	params := url.Values{}
	params.Add("client_id", config.ClientID)
	params.Add("redirect_uri", redirectURI)
	params.Add("response_type", "code")
	params.Add("scope", "openid email profile")
	params.Add("state", state)

	// Add login hint to pre-fill email
	params.Add("login_hint", email)

	return authURL + "?" + params.Encode()
}

// ServeEmailLoginPage serves the email login page
func (h *EmailAuthHandler) ServeEmailLoginPage(w http.ResponseWriter, r *http.Request) {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		slog.Error("error getting embedded assets", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Load email login template
	templateFile, err := fs.ReadFile(staticFS, "templates/company-selection.html")
	if err != nil {
		slog.Error("error reading email login template", "error", err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Load layout template
	layoutFile, err := fs.ReadFile(staticFS, "templates/layout.html")
	if err != nil {
		slog.Error("error reading layout template", "error", err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Parse templates
	tmpl, err := template.New("layout").Parse(string(layoutFile))
	if err != nil {
		slog.Error("error parsing layout template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	_, err = tmpl.New("company-selection").Parse(string(templateFile))
	if err != nil {
		slog.Error("error parsing email login template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Get version from configuration
	version := viper.GetString("version")
	if version == "" {
		version = "development"
	}

	data := struct {
		Version string
	}{
		Version: version,
	}

	// Execute template
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	if err := tmpl.ExecuteTemplate(w, "layout", data); err != nil {
		slog.Error("error executing email login template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

// HandleCallback handles the OAuth callback (can reuse existing callback logic)
func (h *EmailAuthHandler) HandleCallback(w http.ResponseWriter, r *http.Request) {
	// Extract email from state parameter
	state := r.URL.Query().Get("state")
	if !strings.HasPrefix(state, "email_") {
		http.Error(w, "Invalid state parameter", http.StatusBadRequest)
		return
	}

	// Extract email from state
	parts := strings.Split(state, "_")
	if len(parts) < 2 {
		http.Error(w, "Invalid state parameter", http.StatusBadRequest)
		return
	}

	email, err := url.QueryUnescape(parts[1])
	if err != nil {
		http.Error(w, "Invalid email in state", http.StatusBadRequest)
		return
	}

	slog.Info("Processing OAuth callback for email", "email", email)

	// Get the appropriate Keycloak configuration
	keycloakConfig := h.getKeycloakConfigForEmail(email)
	if keycloakConfig == nil {
		http.Error(w, "No authentication configuration found", http.StatusNotFound)
		return
	}

	// Process the OAuth callback using the existing logic
	// For now, delegate to the existing callback handler
	// This would need to be enhanced to use the specific Keycloak config

	// Redirect to main application
	http.Redirect(w, r, "/app", http.StatusFound)
}
