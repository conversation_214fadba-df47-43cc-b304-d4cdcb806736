package handlers

import (
	"encoding/json"
	"log/slog"
	"net/http"
	"strconv"
	"strings"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/connectionpool"
)

type jsonSelectData struct {
	Results []result `json:"results"`
}

type result struct {
	Text  string `json:"text"`
	Value int    `json:"id"`
}

// Employees http-handler
func Employees(w http.ResponseWriter, r *http.Request) {
	q, ok := r.URL.Query()["q"]
	var searchFor string
	if ok {
		searchFor = strings.ToLower(q[0])
	}

	// Get database connection for the current user
	dbClient, err := connectionpool.GetDatabaseConnectionFromRequest(r)
	if err != nil {
		http.Error(w, http.StatusText(500), 500)
		slog.Error("failed to get database connection", "error", err)
		return
	}

	actors, err := dbClient.GetActors()
	if err != nil {
		http.Error(w, http.StatusText(500), 500)
		slog.Error("failed to get actors", "error", err)
		return
	}

	results := []result{}
	if searchFor != "" {
		searchLength := len(searchFor)
		for _, v := range *actors {
			var searchEmpNo string
			if len(strconv.Itoa(v.EmpNo)) >= searchLength {
				searchEmpNo = strconv.Itoa(v.EmpNo)[0:searchLength]
			}

			if searchEmpNo == searchFor || strings.Contains(strings.ToLower(v.Nm), searchFor) {
				data := []string{strconv.Itoa(v.EmpNo), v.Nm}
				text := strings.Join(data, " ")

				results = append(results, result{
					Text:  text,
					Value: v.EmpNo,
				})
			}
		}
	} else {
		for _, v := range *actors {
			data := []string{strconv.Itoa(v.EmpNo), v.Nm}
			text := strings.Join(data, " ")

			results = append(results, result{
				Text:  text,
				Value: v.EmpNo,
			})
		}
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	data := jsonSelectData{
		Results: results,
	}

	b, err := json.Marshal(data)
	if err != nil {
		http.Error(w, http.StatusText(500), 500)
		slog.Error("failed to marshal response", "error", err)
		return
	}
	w.Write(b)
}
