package handlers

import (
	"encoding/json"
	"log/slog"
	"net/http"
	"strconv"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
)

// SettingsHandler handles settings-related HTTP requests
type SettingsHandler struct {
	settingsService *services.SettingsService
}

// NewSettingsHandler creates a new settings handler
func NewSettingsHandler(settingsService *services.SettingsService) *SettingsHandler {
	return &SettingsHandler{
		settingsService: settingsService,
	}
}

// GetFormData handles GET /api/settings/form-data
func (h *SettingsHandler) GetFormData(w http.ResponseWriter, r *http.Request) {
	// Get username from context
	username := getUsernameFromContext(r)
	if username == "" {
		slog.Warn("No username found in context for form data request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get database ID from query parameter (optional for now)
	var databaseID *int
	if dbIDStr := r.URL.Query().Get("database_id"); dbIDStr != "" {
		if dbID, err := strconv.Atoi(dbIDStr); err == nil {
			databaseID = &dbID
		}
	}

	// Get form data from service
	formData, err := h.settingsService.GetFormData(username, databaseID)
	if err != nil {
		slog.Error("Failed to get form data", "error", err, "username", username, "database_id", databaseID)
		http.Error(w, "Failed to get form data", http.StatusInternalServerError)
		return
	}

	// Return as JSON
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(formData); err != nil {
		slog.Error("Failed to encode form data response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	slog.Debug("Retrieved form data", "username", username)
}

// SaveFormData handles POST /api/settings/form-data
func (h *SettingsHandler) SaveFormData(w http.ResponseWriter, r *http.Request) {
	// Get username from context
	username := getUsernameFromContext(r)
	if username == "" {
		slog.Warn("No username found in context for save form data request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get database ID from query parameter (optional for now)
	var databaseID *int
	if dbIDStr := r.URL.Query().Get("database_id"); dbIDStr != "" {
		if dbID, err := strconv.Atoi(dbIDStr); err == nil {
			databaseID = &dbID
		}
	}

	// Parse request body
	var formDataRequest database.FormDataRequest
	if err := json.NewDecoder(r.Body).Decode(&formDataRequest); err != nil {
		slog.Error("Failed to decode form data request", "error", err)
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Save form data
	if err := h.settingsService.SaveFormData(username, databaseID, &formDataRequest); err != nil {
		slog.Error("Failed to save form data", "error", err, "username", username, "database_id", databaseID)
		http.Error(w, "Failed to save form data", http.StatusInternalServerError)
		return
	}

	// Return success response
	response := map[string]interface{}{
		"success": true,
		"message": "Form data saved successfully",
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		slog.Error("Failed to encode save response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	slog.Debug("Saved form data", "username", username)
}

// DeleteFormData handles DELETE /api/settings/form-data
func (h *SettingsHandler) DeleteFormData(w http.ResponseWriter, r *http.Request) {
	// Get username from context
	username := getUsernameFromContext(r)
	if username == "" {
		slog.Warn("No username found in context for delete form data request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get database ID from query parameter (optional for now)
	var databaseID *int
	if dbIDStr := r.URL.Query().Get("database_id"); dbIDStr != "" {
		if dbID, err := strconv.Atoi(dbIDStr); err == nil {
			databaseID = &dbID
		}
	}

	// Delete form data
	if err := h.settingsService.DeleteFormData(username, databaseID); err != nil {
		slog.Error("Failed to delete form data", "error", err, "username", username, "database_id", databaseID)
		http.Error(w, "Failed to delete form data", http.StatusInternalServerError)
		return
	}

	// Return success response
	response := map[string]interface{}{
		"success": true,
		"message": "Form data deleted successfully",
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		slog.Error("Failed to encode delete response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	slog.Debug("Deleted form data", "username", username)
}

// GetUserSetting handles GET /api/settings/{key}
func (h *SettingsHandler) GetUserSetting(w http.ResponseWriter, r *http.Request) {
	// Get username from context
	username := getUsernameFromContext(r)
	if username == "" {
		slog.Warn("No username found in context for get setting request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get setting key from URL path
	key := r.URL.Path[len("/api/settings/"):]
	if key == "" {
		http.Error(w, "Setting key is required", http.StatusBadRequest)
		return
	}

	// Get setting value
	value, err := h.settingsService.GetUserSetting(username, key)
	if err != nil {
		slog.Error("Failed to get user setting", "error", err, "username", username, "key", key)
		http.Error(w, "Failed to get setting", http.StatusInternalServerError)
		return
	}

	// Return as JSON
	response := map[string]interface{}{
		"key":   key,
		"value": value,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		slog.Error("Failed to encode setting response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	slog.Debug("Retrieved user setting", "username", username, "key", key)
}

// SetUserSetting handles POST /api/settings/{key}
func (h *SettingsHandler) SetUserSetting(w http.ResponseWriter, r *http.Request) {
	// Get username from context
	username := getUsernameFromContext(r)
	if username == "" {
		slog.Warn("No username found in context for set setting request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get setting key from URL path
	key := r.URL.Path[len("/api/settings/"):]
	if key == "" {
		http.Error(w, "Setting key is required", http.StatusBadRequest)
		return
	}

	// Parse request body
	var request struct {
		Value string `json:"value"`
	}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		slog.Error("Failed to decode setting request", "error", err)
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Set setting value
	if err := h.settingsService.SetUserSetting(username, key, request.Value); err != nil {
		slog.Error("Failed to set user setting", "error", err, "username", username, "key", key)
		http.Error(w, "Failed to set setting", http.StatusInternalServerError)
		return
	}

	// Return success response
	response := map[string]interface{}{
		"success": true,
		"message": "Setting saved successfully",
		"key":     key,
		"value":   request.Value,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		slog.Error("Failed to encode setting response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	slog.Debug("Set user setting", "username", username, "key", key)
}

// DeleteUserSetting handles DELETE /api/settings/{key}
func (h *SettingsHandler) DeleteUserSetting(w http.ResponseWriter, r *http.Request) {
	// Get username from context
	username := getUsernameFromContext(r)
	if username == "" {
		slog.Warn("No username found in context for delete setting request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get setting key from URL path
	key := r.URL.Path[len("/api/settings/"):]
	if key == "" {
		http.Error(w, "Setting key is required", http.StatusBadRequest)
		return
	}

	// Delete setting
	if err := h.settingsService.DeleteUserSetting(username, key); err != nil {
		slog.Error("Failed to delete user setting", "error", err, "username", username, "key", key)
		http.Error(w, "Failed to delete setting", http.StatusInternalServerError)
		return
	}

	// Return success response
	response := map[string]interface{}{
		"success": true,
		"message": "Setting deleted successfully",
		"key":     key,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		slog.Error("Failed to encode delete response", "error", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	slog.Debug("Deleted user setting", "username", username, "key", key)
}

// getUsernameFromContext extracts username from request context
func getUsernameFromContext(r *http.Request) string {
	if user := r.Context().Value(middleware.UserContextKey); user != nil {
		if username, ok := user.(string); ok {
			return username
		}
	}
	return ""
}
