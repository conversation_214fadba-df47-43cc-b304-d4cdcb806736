package handlers

import (
	"fmt"
	"log/slog"
	"net/http"
	"strings"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
	"github.com/spf13/viper"
)

func formatDate(date int) string {
	slog.Debug("formatting date",
		"date", date,
	)

	if date <= 0 {
		slog.Debug("date is less than or equal to 0, returning empty string")
		return "" // or return a default value like "00.00.0000"
	}

	// Check if the date has the expected 8 digits (YYYYMMDD)
	if date < 10000101 || date > 99991231 {
		slog.Debug("date is not within the expected range, returning empty string")
		return "" // or return a default value
	}

	year := date / 10000
	month := (date / 100) % 100
	day := date % 100

	// Basic validation of month and day
	if month < 1 || month > 12 || day < 1 || day > 31 {
		slog.Debug("month or day is not within the expected range, returning empty string")
		return "" // or return a default value
	}

	slog.Debug("returning formatted date",
		"day", day,
		"month", month,
		"year", year,
	)

	return fmt.Sprintf("%02d.%02d.%d", day, month, year)
}

// Report http-handler
func Report(w http.ResponseWriter, r *http.Request) {
	r.ParseForm()
	slog.Debug("handling report request",
		"type", "HTTP-handler",
		"method", r.Method,
		"url", r.RequestURI,
		"form", r.PostForm,
	)

	var dateFrom string
	if r.PostFormValue("fromDate") != "" {
		sDateFrom := strings.Split(r.PostFormValue("fromDate"), "-")
		dateFrom = fmt.Sprintf("%s%s%s", sDateFrom[2], sDateFrom[1], sDateFrom[0])
	}

	var dateTo string
	if r.PostFormValue("fromDate") != "" {
		sDateTo := strings.Split(r.PostFormValue("toDate"), "-")
		dateTo = fmt.Sprintf("%s%s%s", sDateTo[2], sDateTo[1], sDateTo[0])
	}

	employees := r.PostFormValue("employees")
	salaries := r.PostFormValue("salaries")
	excludedSalaries := r.PostFormValue("excludedSalaries")

	var showExported bool
	if r.PostFormValue("showExported") == "on" {
		showExported = true
	} else {
		showExported = false
	}

	var showHourSalaries bool
	if r.PostFormValue("showHourSalaries") == "on" {
		showHourSalaries = true
	} else {
		showHourSalaries = false
	}

	var showMonthlySalaries bool
	if r.PostFormValue("showMonthlySalaries") == "on" {
		showMonthlySalaries = true
	} else {
		showMonthlySalaries = false
	}

	dbClient, ok := viper.Get("visma.client").(*vismadb.Client)
	if !ok {
		http.Error(w, http.StatusText(500), 500)
		slog.Error("failed to get visma client",
			"type", "HTTP-handler",
			"method", r.Method,
			"url", r.RequestURI,
		)
		return
	}

	if dateFrom != "" && dateTo != "" {
		var hourSalaries *[]vismadb.OrdLn
		var monthlySalaries *[]vismadb.OrdLn
		var err error

		if showHourSalaries {
			hourSalaries, _, err = dbClient.GetHourSalaries(employees, dateFrom, dateTo, showExported)
			if err != nil {
				http.Error(w, http.StatusText(500), 500)
				slog.Error("failed to get hour salaries",
					"type", "HTTP-handler",
					"method", r.Method,
					"url", r.RequestURI,
					"error", err,
				)
				return
			}
		}

		if showMonthlySalaries {
			monthlySalaries, _, err = dbClient.GetMonthlySalaries(employees, salaries, excludedSalaries, dateFrom, dateTo, showExported)
			if err != nil {
				http.Error(w, http.StatusText(500), 500)
				slog.Error("failed to get monthly salaries",
					"type", "HTTP-handler",
					"method", r.Method,
					"url", r.RequestURI,
					"error", err,
				)
				return
			}
		}

		// Combine hourSalaries and monthlySalaries
		var orderlines []vismadb.OrdLn
		if hourSalaries != nil {
			orderlines = append(orderlines, *hourSalaries...)
		}
		if monthlySalaries != nil {
			orderlines = append(orderlines, *monthlySalaries...)
		}

		var contents []string

		head := fmt.Sprint("<table class=\"table\">" +
			"<thead class=\"thead-dark\"> " +
			"<tr>" +
			"<th scope=\"col\">#</th>" +
			"<th scope=\"col\">Ansatt</th>" +
			"<th scope=\"col\">Lønnsart</th>" +
			"<th scope=\"col\">Antall</th>" +
			"<th scope=\"col\">Løpenummer</th>" +
			"<th scope=\"col\">Dato</th>" +
			"</tr>" +
			"</thead>" +
			"<tbody>")

		contents = append(contents, head)

		var rows []string
		for _, v := range orderlines {
			row := fmt.Sprintf("<tr><td>%d</td><td>%d</td><td>%s</td><td>%.2f</td><td>%.0f</td><td>%s</td></tr>",
				v.OrdNo, v.EmpNo, v.WageSrt, v.Qty, v.Free4, formatDate(v.TrDt))
			rows = append(rows, row)
		}

		contents = append(contents, strings.Join(rows, ""))
		contents = append(contents, "</tbody></table>")

		w.Header().Set("Content-Type", "text/html")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(strings.Join(contents, "")))
	} else {
		http.Error(w, http.StatusText(500), 500)
		slog.Error("dates must be set",
			"type", "HTTP-handler",
			"method", r.Method,
			"url", r.RequestURI,
		)
		return
	}
}
