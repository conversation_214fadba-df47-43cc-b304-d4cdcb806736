package handlers

import (
	"fmt"
	"html/template"
	"io/fs"
	"log/slog"
	"net/http"
	"os"
	"path"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/connectionpool"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"github.com/probits-as/erv-go-lonn-exporter/public"
	"github.com/spf13/viper"
)

// ServeTemplates ... kind of just serves /
func ServeTemplates(w http.ResponseWriter, r *http.Request) {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		slog.Error("error getting embedded assets", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Clean and prepare paths
	cleanPath := path.Clean(r.URL.Path)
	if cleanPath == "/" {
		cleanPath = "/index.html"
	}

	// Construct template paths
	layoutPath := "templates/layout.html"
	templatePath := "templates" + cleanPath

	// Check if template exists
	if _, err := fs.Stat(staticFS, templatePath); err != nil {
		if os.IsNotExist(err) {
			http.NotFound(w, r)
			return
		}
		slog.Error("error checking template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Parse templates from embedded filesystem
	tmpl, err := template.ParseFS(staticFS, layoutPath, templatePath)
	if err != nil {
		slog.Error("error parsing template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Get username from context if available
	username := ""
	if user := r.Context().Value(middleware.UserContextKey); user != nil {
		username = user.(string)
	}

	// Get user's current company context
	var companyName, database string
	if manager, err := connectionpool.GetConnectionManagerFromContext(r.Context()); err == nil {
		if userCtx, err := connectionpool.GetUserContextFromRequest(r, manager); err == nil && userCtx.CompanyID != nil {
			// Get company details from connection manager
			if companyID, err := connectionpool.GetUserCompanyFromContext(r.Context()); err == nil {
				// For now, use company ID as database name - this can be enhanced later
				database = fmt.Sprintf("Company_%d", companyID)
				companyName = fmt.Sprintf("Company %d", companyID) // This should be fetched from company service
			}
		}
	}

	// Fallback to default values if no company context
	if companyName == "" {
		companyName = "No Company Selected"
	}
	if database == "" {
		database = "No Database"
	}

	data := struct {
		Version     string
		CompanyName string
		Database    string
		Username    string
	}{
		viper.GetString("version"),
		companyName,
		database,
		username,
	}

	if err := tmpl.ExecuteTemplate(w, "layout", data); err != nil {
		slog.Error("error executing template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

// ServeLoginPage serves the login page
func ServeLoginPage(w http.ResponseWriter, r *http.Request) {
	// Get embedded filesystem
	staticFS, err := public.Assets()
	if err != nil {
		slog.Error("error getting embedded assets", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Load login template
	templateFile, err := fs.ReadFile(staticFS, "templates/login.html")
	if err != nil {
		slog.Error("error reading login template", "error", err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Load layout template
	layoutFile, err := fs.ReadFile(staticFS, "templates/layout.html")
	if err != nil {
		slog.Error("error reading layout template", "error", err)
		http.Error(w, http.StatusText(http.StatusNotFound), http.StatusNotFound)
		return
	}

	// Parse templates
	tmpl, err := template.New("layout").Parse(string(layoutFile))
	if err != nil {
		slog.Error("error parsing layout template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	tmpl, err = tmpl.Parse(string(templateFile))
	if err != nil {
		slog.Error("error parsing login template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	data := struct {
		Version string
	}{
		viper.GetString("version"),
	}

	if err := tmpl.ExecuteTemplate(w, "layout", data); err != nil {
		slog.Error("error executing login template", "error", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}
