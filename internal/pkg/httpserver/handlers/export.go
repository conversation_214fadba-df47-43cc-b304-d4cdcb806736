package handlers

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"strings"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/connectionpool"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/exporter"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
	"github.com/spf13/viper"
)

// Export http-handler
func Export(w http.ResponseWriter, r *http.Request) {
	r.ParseForm()
	slog.Debug("handling export request",
		"type", "HTTP-handler",
		"method", r.Method,
		"url", r.RequestURI,
		"form", r.PostForm,
	)

	var dateFrom string
	if r.PostFormValue("fromDate") != "" {
		sDateFrom := strings.Split(r.PostFormValue("fromDate"), "-")
		dateFrom = fmt.Sprintf("%s%s%s", sDateFrom[2], sDateFrom[1], sDateFrom[0])
	}

	var dateTo string
	if r.PostFormValue("fromDate") != "" {
		sDateTo := strings.Split(r.PostFormValue("toDate"), "-")
		dateTo = fmt.Sprintf("%s%s%s", sDateTo[2], sDateTo[1], sDateTo[0])
	}

	employees := r.PostFormValue("employees")
	salaries := r.PostFormValue("salaries")
	excludedSalaries := r.PostFormValue("excludedSalaries")

	var showExported bool
	if r.PostFormValue("showExported") == "on" {
		showExported = true
	} else {
		showExported = false
	}

	var showHourSalaries bool
	if r.PostFormValue("showHourSalaries") == "on" {
		showHourSalaries = true
	} else {
		showHourSalaries = false
	}

	var showMonthlySalaries bool
	if r.PostFormValue("showMonthlySalaries") == "on" {
		showMonthlySalaries = true
	} else {
		showMonthlySalaries = false
	}

	// Get database connection for the current user
	dbClient, err := connectionpool.GetDatabaseConnectionFromRequest(r)
	if err != nil {
		http.Error(w, http.StatusText(500), 500)
		slog.Error("failed to get database connection",
			"type", "HTTP-handler",
			"method", r.Method,
			"url", r.RequestURI,
			"error", err,
		)
		return
	}

	if dateFrom != "" && dateTo != "" {
		var hourSalaries *[]vismadb.OrdLn
		var hourSalariesIDs map[int][]int
		var monthlySalaries *[]vismadb.OrdLn
		var monthlySalariesIDs map[int][]int
		var err error

		if showHourSalaries {
			hourSalaries, hourSalariesIDs, err = dbClient.GetHourSalaries(employees, dateFrom, dateTo, showExported)
			if err != nil {
				http.Error(w, http.StatusText(500), 500)
				slog.Error("error getting hour salaries",
					"type", "HTTP-handler",
					"method", r.Method,
					"url", r.RequestURI,
					"error", err,
				)
				return
			}
		}

		if showMonthlySalaries {
			monthlySalaries, monthlySalariesIDs, err = dbClient.GetMonthlySalaries(employees, salaries, excludedSalaries, dateFrom, dateTo, showExported)
			if err != nil {
				http.Error(w, http.StatusText(500), 500)
				slog.Error("error getting monthly salaries",
					"type", "HTTP-handler",
					"method", r.Method,
					"url", r.RequestURI,
					"error", err,
				)
				return
			}
		}

		// Combine hourSalaries and monthlySalaries
		var orderlines []vismadb.OrdLn
		if hourSalaries != nil {
			orderlines = append(orderlines, *hourSalaries...)
		}
		if monthlySalaries != nil {
			orderlines = append(orderlines, *monthlySalaries...)
		}

		// Combine hourSalariesIDs and monthlySalariesIDs
		orderIDs := make(map[int][]int)
		for k, v := range hourSalariesIDs {
			orderIDs[k] = v
		}
		for k, v := range monthlySalariesIDs {
			orderIDs[k] = v
		}

		// Export orderlines to file
		exporter := exporter.NewVismaExportConfig(exporter.VLSTANDARD, viper.GetString("export.file"))

		if err := exporter.ExportOrderLinesToFile(&orderlines); err != nil {
			http.Error(w, http.StatusText(500), 500)
			slog.Error("error exporting order lines to file",
				"type", "HTTP-handler",
				"method", r.Method,
				"url", r.RequestURI,
				"error", err,
			)
			return
		}

		free4, err := dbClient.GetNewFree4()
		if err != nil {
			http.Error(w, http.StatusText(500), 500)
			slog.Error("error getting new free4",
				"type", "HTTP-handler",
				"method", r.Method,
				"url", r.RequestURI,
				"error", err,
			)
			return
		}

		slog.Debug("new free4",
			"type", "HTTP-handler",
			"method", r.Method,
			"url", r.RequestURI,
			"free4", free4,
		)

		result, err := dbClient.UpdateFree4(free4, orderIDs)
		if err != nil {
			http.Error(w, http.StatusText(500), 500)
			slog.Error("error updating free4",
				"type", "HTTP-handler",
				"method", r.Method,
				"url", r.RequestURI,
				"error", err,
			)
			return
		}

		slog.Debug("rows affected",
			"type", "HTTP-handler",
			"method", r.Method,
			"url", r.RequestURI,
			"rows", result,
		)

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		data := struct {
			Message string `json:"message"`
			Code    int    `json:"code"`
		}{
			fmt.Sprintf("OK - Rows affected (%d)", result),
			200,
		}
		b, err := json.Marshal(data)
		if err != nil {
			http.Error(w, http.StatusText(500), 500)
			slog.Error("error marshalling response",
				"type", "HTTP-handler",
				"method", r.Method,
				"url", r.RequestURI,
				"error", err,
			)
			return
		}
		w.Write(b)
	} else {
		http.Error(w, http.StatusText(500), 500)
		slog.Error("dates must be set",
			"type", "HTTP-handler",
			"method", r.Method,
			"url", r.RequestURI,
		)
		return
	}
}
