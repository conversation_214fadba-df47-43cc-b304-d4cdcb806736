package handlers

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"
	"time"

	"github.com/Nerzal/gocloak/v13"
	"github.com/golang-jwt/jwt/v5"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"golang.org/x/oauth2"
)

// AuthConfig holds the configuration for OAuth2 authentication
type AuthConfig struct {
	KeycloakURL    string
	Realm          string
	ClientID       string
	ClientSecret   string
	RequiredRole   string
	RedirectURL    string
	SessionStore   *middleware.SessionStore
	KeycloakClient *middleware.KeycloakClient
}

var authConfig *AuthConfig

// SetAuthConfig sets the global auth configuration
func SetAuthConfig(config *AuthConfig) {
	authConfig = config
}

// getOAuth2Config returns the OAuth2 configuration for Keycloak
func getOAuth2Config() *oauth2.Config {
	if authConfig == nil {
		return nil
	}

	return &oauth2.Config{
		ClientID:     authConfig.ClientID,
		ClientSecret: authConfig.ClientSecret,
		RedirectURL:  authConfig.RedirectURL,
		Scopes:       []string{"openid", "profile", "email"},
		Endpoint: oauth2.Endpoint{
			AuthURL:  fmt.Sprintf("%s/realms/%s/protocol/openid-connect/auth", authConfig.KeycloakURL, authConfig.Realm),
			TokenURL: fmt.Sprintf("%s/realms/%s/protocol/openid-connect/token", authConfig.KeycloakURL, authConfig.Realm),
		},
	}
}

// generateState generates a random state parameter for OAuth2
func generateState() string {
	b := make([]byte, 32)
	rand.Read(b)
	return base64.URLEncoding.EncodeToString(b)
}

// Login handles the OAuth2 login initiation
func Login(w http.ResponseWriter, r *http.Request) {
	oauth2Config := getOAuth2Config()
	if oauth2Config == nil {
		slog.Error("OAuth2 configuration not available")
		http.Error(w, "Authentication not configured", http.StatusInternalServerError)
		return
	}

	// Get session
	session := middleware.GetSession(r)
	if session == nil {
		slog.Error("Session not available")
		http.Error(w, "Session error", http.StatusInternalServerError)
		return
	}

	// Generate and store state parameter
	state := generateState()
	session.Values["oauth_state"] = state

	// Save session
	if err := middleware.SaveSession(r, w, session); err != nil {
		slog.Error("Failed to save session", "error", err)
		http.Error(w, "Session error", http.StatusInternalServerError)
		return
	}

	// Redirect to Keycloak
	authURL := oauth2Config.AuthCodeURL(state, oauth2.AccessTypeOffline)
	http.Redirect(w, r, authURL, http.StatusFound)
}

// Callback handles the OAuth2 callback from Keycloak
func Callback(w http.ResponseWriter, r *http.Request) {
	oauth2Config := getOAuth2Config()
	if oauth2Config == nil {
		slog.Error("OAuth2 configuration not available")
		http.Error(w, "Authentication not configured", http.StatusInternalServerError)
		return
	}

	// Get session
	session := middleware.GetSession(r)
	if session == nil {
		slog.Error("Session not available")
		http.Error(w, "Session error", http.StatusInternalServerError)
		return
	}

	// Verify state parameter
	state := r.URL.Query().Get("state")
	sessionState, ok := session.Values["oauth_state"].(string)
	if !ok || state != sessionState {
		slog.Warn("Invalid state parameter", "received", state, "expected", sessionState)
		http.Error(w, "Invalid state parameter", http.StatusBadRequest)
		return
	}

	// Clear the state from session
	delete(session.Values, "oauth_state")

	// Get authorization code
	code := r.URL.Query().Get("code")
	if code == "" {
		slog.Warn("No authorization code received")
		http.Error(w, "No authorization code", http.StatusBadRequest)
		return
	}

	// Exchange code for tokens
	ctx := context.Background()
	token, err := oauth2Config.Exchange(ctx, code)
	if err != nil {
		slog.Error("Failed to exchange code for token", "error", err)
		http.Error(w, "Token exchange failed", http.StatusInternalServerError)
		return
	}

	// Validate the access token and extract user info
	accessToken := token.AccessToken
	claims, err := validateTokenWithKeycloak(ctx, accessToken)
	if err != nil {
		slog.Error("Failed to validate token", "error", err)
		http.Error(w, "Token validation failed", http.StatusUnauthorized)
		return
	}

	// Check for required role
	hasAccess, err := hasRequiredRole(claims, authConfig.RequiredRole)
	if err != nil || !hasAccess {
		username, _ := claims["preferred_username"].(string)
		slog.Warn("Insufficient permissions", "user", username, "required_role", authConfig.RequiredRole)
		http.Error(w, "Insufficient permissions", http.StatusForbidden)
		return
	}

	// Extract user information
	username, _ := claims["preferred_username"].(string)
	if username == "" {
		username = "unknown"
	}

	// Store user info in session
	expiresAt := time.Now().Add(time.Duration(token.Expiry.Sub(time.Now())))
	refreshToken := ""
	if token.RefreshToken != "" {
		refreshToken = token.RefreshToken
	}

	slog.Debug("Setting user info in session", "session_id", session.ID, "username", username)
	middleware.SetUserInfo(session, accessToken, refreshToken, username, expiresAt)

	// Save session
	if err := middleware.SaveSession(r, w, session); err != nil {
		slog.Error("Failed to save session", "error", err)
		http.Error(w, "Session error", http.StatusInternalServerError)
		return
	}

	slog.Info("User successfully authenticated", "username", username, "session_id", session.ID)

	// Redirect to home page
	http.Redirect(w, r, "/", http.StatusFound)
}

// Logout handles user logout
func Logout(w http.ResponseWriter, r *http.Request) {
	// Get session
	session := middleware.GetSession(r)
	if session != nil {
		// Clear user info from session
		middleware.ClearUserInfo(session)

		// Save session
		if err := middleware.SaveSession(r, w, session); err != nil {
			slog.Error("Failed to save session during logout", "error", err)
		}
	}

	// Construct Keycloak logout URL
	if authConfig != nil {
		logoutURL := fmt.Sprintf("%s/realms/%s/protocol/openid-connect/logout", authConfig.KeycloakURL, authConfig.Realm)

		// Add redirect_uri parameter to redirect back to our app after logout
		redirectURI := getBaseURL(r) + "/"
		logoutURL += "?redirect_uri=" + url.QueryEscape(redirectURI)

		http.Redirect(w, r, logoutURL, http.StatusFound)
		return
	}

	// Fallback: redirect to home page
	http.Redirect(w, r, "/", http.StatusFound)
}

// getBaseURL extracts the base URL from the request
func getBaseURL(r *http.Request) string {
	scheme := "http"
	if r.TLS != nil {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s", scheme, r.Host)
}

// validateTokenWithKeycloak validates a token using the Keycloak client
func validateTokenWithKeycloak(ctx context.Context, tokenString string) (jwt.MapClaims, error) {
	if authConfig == nil {
		return nil, fmt.Errorf("auth config not available")
	}

	// Create a temporary Keycloak client for validation
	client := gocloak.NewClient(authConfig.KeycloakURL)

	// Introspect the token to validate it
	result, err := client.RetrospectToken(ctx, tokenString, authConfig.ClientID, authConfig.ClientSecret, authConfig.Realm)
	if err != nil {
		return nil, fmt.Errorf("failed to introspect token: %w", err)
	}

	if result.Active == nil || !*result.Active {
		return nil, fmt.Errorf("token is not active")
	}

	// Parse the token to get claims (without validation since we already validated via introspection)
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token claims: %w", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("failed to extract claims")
	}

	return claims, nil
}

// hasRequiredRole checks if the user has the required role
func hasRequiredRole(claims jwt.MapClaims, requiredRole string) (bool, error) {
	// Check in realm roles
	if realmRoles, ok := claims["realm_access"].(map[string]interface{}); ok {
		if roles, ok := realmRoles["roles"].([]interface{}); ok {
			for _, role := range roles {
				if role.(string) == requiredRole {
					return true, nil
				}
			}
		}
	}

	// Check in client roles (resource_access)
	if resourceAccess, ok := claims["resource_access"].(map[string]interface{}); ok {
		for _, clientRoles := range resourceAccess {
			if client, ok := clientRoles.(map[string]interface{}); ok {
				if roles, ok := client["roles"].([]interface{}); ok {
					for _, role := range roles {
						if role.(string) == requiredRole {
							return true, nil
						}
					}
				}
			}
		}
	}

	// Check in groups
	if groups, ok := claims["groups"].([]interface{}); ok {
		for _, group := range groups {
			if group.(string) == requiredRole {
				return true, nil
			}
		}
	}

	return false, nil
}
