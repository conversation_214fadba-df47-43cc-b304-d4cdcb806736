package handlers

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"net/url"
	"time"

	"github.com/Nerzal/gocloak/v13"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
)

// MultiTenantAuthHandler handles multi-tenant authentication with company selection
type MultiTenantAuthHandler struct {
	companyService *services.CompanyService
}

// NewMultiTenantAuthHandler creates a new multi-tenant auth handler
func NewMultiTenantAuthHandler(companyService *services.CompanyService) *MultiTenantAuthHandler {
	return &MultiTenantAuthHandler{
		companyService: companyService,
	}
}

// HandleLogin handles login requests with company selection
func (h *MultiTenantAuthHandler) HandleLogin(w http.ResponseWriter, r *http.Request) {
	// Get company parameter from query string
	companyName := r.URL.Query().Get("company")
	if companyName == "" {
		// Redirect to company selection page
		http.Redirect(w, r, "/", http.StatusFound)
		return
	}

	// Get company configuration
	company, err := h.companyService.GetCompanyByName(companyName)
	if err != nil {
		slog.Error("Failed to get company", "company", companyName, "error", err)
		http.Error(w, "Company not found", http.StatusNotFound)
		return
	}

	if company == nil {
		slog.Warn("Company not found", "company", companyName)
		http.Error(w, "Company not found", http.StatusNotFound)
		return
	}

	if !company.Enabled {
		slog.Warn("Company is disabled", "company", companyName)
		http.Error(w, "Company is not available", http.StatusForbidden)
		return
	}

	// Build authorization URL
	authURL := fmt.Sprintf("%s/realms/%s/protocol/openid-connect/auth",
		company.KeycloakURL, company.KeycloakRealm)

	// Get the current request URL for redirect_uri
	scheme := "http"
	if r.TLS != nil {
		scheme = "https"
	}
	redirectURI := fmt.Sprintf("%s://%s/auth/callback?company=%s", scheme, r.Host, companyName)

	// Build authorization parameters
	params := url.Values{}
	params.Set("client_id", company.KeycloakClientID)
	params.Set("redirect_uri", redirectURI)
	params.Set("response_type", "code")
	params.Set("scope", "openid profile email")

	// Add state parameter for security (should include company info)
	state := fmt.Sprintf("company=%s&timestamp=%d", companyName, time.Now().Unix())
	params.Set("state", state)

	// Redirect to Keycloak authorization endpoint
	fullAuthURL := fmt.Sprintf("%s?%s", authURL, params.Encode())

	slog.Info("Redirecting to Keycloak for authentication",
		"company", companyName,
		"realm", company.KeycloakRealm,
		"redirect_uri", redirectURI)

	http.Redirect(w, r, fullAuthURL, http.StatusFound)
}

// HandleCallback handles the OAuth callback from Keycloak
func (h *MultiTenantAuthHandler) HandleCallback(w http.ResponseWriter, r *http.Request) {
	// Get company parameter
	companyName := r.URL.Query().Get("company")
	if companyName == "" {
		slog.Error("Missing company parameter in callback")
		http.Error(w, "Invalid callback request", http.StatusBadRequest)
		return
	}

	// Get company configuration
	company, err := h.companyService.GetCompanyByName(companyName)
	if err != nil {
		slog.Error("Failed to get company in callback", "company", companyName, "error", err)
		http.Error(w, "Company not found", http.StatusNotFound)
		return
	}

	if company == nil {
		slog.Error("Company not found in callback", "company", companyName)
		http.Error(w, "Company not found", http.StatusNotFound)
		return
	}

	// Get authorization code
	code := r.URL.Query().Get("code")
	if code == "" {
		slog.Error("Missing authorization code in callback")
		http.Error(w, "Missing authorization code", http.StatusBadRequest)
		return
	}

	// Verify state parameter
	state := r.URL.Query().Get("state")
	expectedStatePrefix := fmt.Sprintf("company=%s&timestamp=", companyName)
	if !startsWith(state, expectedStatePrefix) {
		slog.Error("Invalid state parameter", "state", state, "expected_prefix", expectedStatePrefix)
		http.Error(w, "Invalid state parameter", http.StatusBadRequest)
		return
	}

	// Create Keycloak client for this company
	keycloakClient := gocloak.NewClient(company.KeycloakURL)

	// Build redirect URI
	scheme := "http"
	if r.TLS != nil {
		scheme = "https"
	}
	redirectURI := fmt.Sprintf("%s://%s/auth/callback?company=%s", scheme, r.Host, companyName)

	// Exchange authorization code for tokens
	ctx := context.Background()
	token, err := keycloakClient.GetToken(ctx, company.KeycloakRealm, gocloak.TokenOptions{
		ClientID:     &company.KeycloakClientID,
		ClientSecret: &company.KeycloakClientSecret,
		Code:         &code,
		RedirectURI:  &redirectURI,
		GrantType:    gocloak.StringP("authorization_code"),
	})

	if err != nil {
		slog.Error("Failed to exchange code for token", "company", companyName, "error", err)
		http.Error(w, "Authentication failed", http.StatusUnauthorized)
		return
	}

	// Get user info from token
	userInfo, err := keycloakClient.GetUserInfo(ctx, token.AccessToken, company.KeycloakRealm)
	if err != nil {
		slog.Error("Failed to get user info", "company", companyName, "error", err)
		http.Error(w, "Failed to get user info", http.StatusUnauthorized)
		return
	}

	// Store authentication info in session
	session := middleware.GetSession(r)
	if session == nil {
		slog.Error("No session available for storing auth info")
		http.Error(w, "Session error", http.StatusInternalServerError)
		return
	}

	// Calculate token expiry
	expiresAt := time.Now().Add(time.Duration(token.ExpiresIn) * time.Second)

	// Store user info in session
	username := ""
	if userInfo.PreferredUsername != nil {
		username = *userInfo.PreferredUsername
	} else if userInfo.Email != nil {
		username = *userInfo.Email
	}

	if username == "" {
		slog.Error("No username found in user info")
		http.Error(w, "Invalid user info", http.StatusUnauthorized)
		return
	}

	// Store tokens and user info in session
	middleware.SetUserInfo(session, token.AccessToken, token.RefreshToken, username, expiresAt)

	// Save session
	if err := middleware.SaveSession(r, w, session); err != nil {
		slog.Error("Failed to save session", "error", err)
		http.Error(w, "Session error", http.StatusInternalServerError)
		return
	}

	slog.Info("User authenticated successfully",
		"username", username,
		"company", companyName,
		"realm", company.KeycloakRealm)

	// Redirect to main application
	http.Redirect(w, r, "/app", http.StatusFound)
}

// HandleLogout handles logout requests
func (h *MultiTenantAuthHandler) HandleLogout(w http.ResponseWriter, r *http.Request) {
	// Get session
	session := middleware.GetSession(r)
	if session != nil {
		// Clear user info from session
		middleware.ClearUserInfo(session)

		// Save session
		if err := middleware.SaveSession(r, w, session); err != nil {
			slog.Error("Failed to save session during logout", "error", err)
		}
	}

	slog.Info("User logged out")

	// Redirect to company selection page
	http.Redirect(w, r, "/", http.StatusFound)
}

// Helper function to check if string starts with prefix
func startsWith(s, prefix string) bool {
	return len(s) >= len(prefix) && s[:len(prefix)] == prefix
}
