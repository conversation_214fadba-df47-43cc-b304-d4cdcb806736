package exporter

import (
	"fmt"
	"log/slog"
	"os"
	"strings"

	"github.com/spf13/viper"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
)

// VismaExportType type/layout of the Visma-export file
type VismaExportType int

const (
	// VLSTANDARD output file
	// streng = (WgYr & ";" & RunNo & ";" & EmpNo & ";" & WgSrt & ";" & Bas & ";" & Qty & ";" & wRate & ";" & Am & ";" & AcNo & ";" & AgAcNo & ";" & R1 & ";" & R2 & ";" & EmptyString & ";" & R4 & ";" & R5 & ";" & R6 & ";" & DedCd)
	VLSTANDARD VismaExportType = 1 + iota
)

// VismaExporterConfig holds configuration options for the visma-exporter
type VismaExporterConfig struct {
	FileFormat VismaExportType
	File       string
	Overwrite  bool
}

// VismaField represents a field in the Visma import format
type VismaField struct {
	Name     string
	Value    interface{}
	Format   string // How to format the value (e.g., "%d", "%.2f", etc.)
	IsQuoted bool   // Whether to wrap the empty value in quotes
	Disabled bool   // Whether to exclude the field from the output
}

// VismaExportLine represents a complete line of Visma export data
type VismaExportLine struct {
	Fields []VismaField
}

const vismaExportTemplate = `{{range .Fields}}{{if ne .Position 0}};{{end}}{{with .Value}}{{.}}{{else}}{{end}}{{end}}`

// formatVismaLine creates a properly formatted line for Visma import
func formatVismaLine(v vismadb.OrdLn) (string, error) {
	// Define the fields in order. This makes it easy to modify the structure
	fields := []VismaField{
		{Name: "WgYr", Value: "0", Format: "%s"},                       // Field 1: Lønnsår, må være 0
		{Name: "RunNo", Value: v.RunNo, Format: "%d"},                  // Field 2: Lønnskjørenummer, må være 0
		{Name: "EmpNo", Value: v.EmpNo, Format: "%d"},                  // Field 3: Ansattnummer
		{Name: "WgSrt", Value: v.WageSrt, Format: "%s"},                // Field 4: Lønnsart
		{Name: "Bas", Value: "", Format: "%.2f"},                       // Field 5: Grunnlag
		{Name: "Qty", Value: v.Qty, Format: "%.2f"},                    // Field 6: Antall
		{Name: "wRate", Value: "", Format: "%s"},                       // Field 7: Sats
		{Name: "Am", Value: "", Format: "%s"},                          // Field 8: Beløp
		{Name: "AcNo", Value: "", Format: "%s"},                        // Field 9: Konto
		{Name: "AgAcNo", Value: "", Format: "%s"},                      // Field 10: Motkonto
		{Name: "R1", Value: "", Format: "%s", IsQuoted: true},          // Field 11: Kostnadsbærere
		{Name: "R2", Value: "", Format: "%s", IsQuoted: true},          // Field 12: Kostnadsbærere
		{Name: "EmptyString", Value: "", Format: "%s", IsQuoted: true}, // Field 13: Kostnadsbærere
		{Name: "R4", Value: "", Format: "%s", IsQuoted: true},          // Field 14: Kostnadsbærere
		{Name: "R5", Value: "", Format: "%s", IsQuoted: true},          // Field 15: Kostnadsbærere
		{Name: "R6", Value: "", Format: "%s", IsQuoted: true},          // Field 16: Kostnadsbærere
		{Name: "DedCd", Value: "", Format: "%s", IsQuoted: false},      // Field 17: Kostnadsbærere
		{Name: "Date", Value: formatDate(v.TrDt), Format: "%s"},        // Field 18: Transaksjonsdato
		{Name: "Comments", Value: "", Format: "%s", Disabled: true},    // Field 19: Kommentar
		{Name: "Extra", Value: "", Format: "%s", Disabled: true},       // Field 20: Tilleggsinfo
	}

	// Build the output string
	var values []string
	for _, field := range fields {
		if field.Disabled {
			continue
		}
		var formatted string
		if field.Value == "" {
			if field.IsQuoted {
				formatted = `""`
			} else {
				formatted = ""
			}
		} else {
			formatted = fmt.Sprintf(field.Format, field.Value)
		}
		values = append(values, formatted)
	}

	return strings.Join(values, ";"), nil
}

// ExportToVisma exports the orderlines to Visma format
func ExportToVisma(orderlines *[]vismadb.OrdLn) ([]string, error) {
	var sData []string
	for _, v := range *orderlines {
		line, err := formatVismaLine(v)
		if err != nil {
			return nil, fmt.Errorf("error formatting line for employee %d: %w", v.EmpNo, err)
		}
		sData = append(sData, line)
	}
	return sData, nil
}

func formatDate(date int) string {
	slog.Debug("exporter.VismaExporterConfig", "date", date)

	if date <= 0 {
		slog.Debug("exporter.VismaExporterConfig, date is less than or equal to 0, returning empty string", "date", date)
		return "" // or return a default value like "00.00.0000"
	}

	// Check if the date has the expected 8 digits (YYYYMMDD)
	if date < 10000101 || date > 99991231 {
		slog.Debug("exporter.VismaExporterConfig, date is not within the expected range, returning empty string", "date", date)
		return "" // or return a default value
	}

	year := date / 10000
	month := (date / 100) % 100
	day := date % 100

	// Basic validation of month and day
	if month < 1 || month > 12 || day < 1 || day > 31 {
		slog.Debug("exporter.VismaExporterConfig, month or day is not within the expected range, returning empty string", "date", date)
		return "" // or return a default value
	}

	slog.Debug("exporter.VismaExporterConfig, returning formatted date", "date", fmt.Sprintf("%02d.%02d.%d", day, month, year))

	return fmt.Sprintf("%02d.%02d.%d", day, month, year)
}

// NewVismaExportConfig returns a configuration struct to be filled in
func NewVismaExportConfig(fileFormat VismaExportType, file string) *VismaExporterConfig {
	return &VismaExporterConfig{FileFormat: fileFormat, File: file, Overwrite: viper.GetBool("export.overwrite")}
}

// ExportOrderLinesToFile exports orderlines to a file in the specified Visma format
func (cfg *VismaExporterConfig) ExportOrderLinesToFile(orderlines *[]vismadb.OrdLn) error {
	switch cfg.FileFormat {
	case VLSTANDARD:
		// Stat returns file info. It will return
		// an error if there is no file.
		fileInfo, err := os.Stat(cfg.File)
		if err != nil {
			if os.IsNotExist(err) {
				slog.Debug("exporter.VismaExporterConfig", "file does not exist", cfg.File)
			} else {
				slog.Debug("exporter.VismaExporterConfig", "an error occured when checking for the file", cfg.File, "error", err)
				return err
			}
		}

		if fileInfo != nil {
			if !cfg.Overwrite {
				slog.Debug("exporter.VismaExporterConfig", "file exists and is overwrite protected", cfg.File, "fileInfo", fileInfo)
				return fmt.Errorf("%s exist and is overwrite protected", cfg.File)
			}
			slog.Debug("exporter.VismaExporterConfig", "file exists and is not overwrite protected", cfg.File, "fileInfo", fileInfo)
		}

		// Open a new file for writing only
		file, err := os.OpenFile(
			cfg.File,
			os.O_WRONLY|os.O_TRUNC|os.O_CREATE,
			0666,
		)
		if err != nil {
			slog.Debug("exporter.VismaExporterConfig", "unable to open file", cfg.File, "error", err)
			return err
		}
		defer file.Close()

		// Use our new ExportToVisma function to generate the lines
		sData, err := ExportToVisma(orderlines)
		if err != nil {
			return fmt.Errorf("failed to format export data: %w", err)
		}

		// Write bytes to file
		byteSlice := []byte(strings.Join(sData, "\n"))
		bytesWritten, err := file.Write(byteSlice)
		if err != nil {
			slog.Debug("exporter.VismaExporterConfig", "failed to write to file", cfg.File, "error", err)
			return err
		}
		slog.Debug("exporter.VismaExporterConfig", "file saved", cfg.File, "bytesWritten", bytesWritten)
	default:
		return fmt.Errorf("unknown export-format")
	}
	return nil
}
