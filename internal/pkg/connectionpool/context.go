package connectionpool

import (
	"context"
	"fmt"
	"net/http"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver/middleware"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
)

// ContextKey is a type for context keys to avoid collisions
type Context<PERSON><PERSON> string

const (
	// ConnectionManagerKey is the context key for the connection manager
	ConnectionManagerKey ContextKey = "connection_manager"
	// UserCompanyKey is the context key for the user's current company ID
	UserCompanyKey ContextKey = "user_company"
)

// UserContext provides easy access to user-specific database connections
type UserContext struct {
	Username  string
	CompanyID *int
	manager   *ConnectionManager
}

// GetUserContextFromRequest extracts user context from an HTTP request
func GetUserContextFromRequest(r *http.Request, manager *ConnectionManager) (*UserContext, error) {
	// Get username from middleware context
	username := ""
	if user := r.Context().Value(middleware.UserContextKey); user != nil {
		if u, ok := user.(string); ok {
			username = u
		}
	}
	
	if username == "" {
		return nil, fmt.Errorf("no authenticated user found in request context")
	}
	
	// Get user's current company from session
	session, err := manager.sessionService.GetUserSession(username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user session: %w", err)
	}
	
	userCtx := &UserContext{
		Username: username,
		manager:  manager,
	}
	
	if session != nil {
		userCtx.CompanyID = session.CurrentCompanyID
	}
	
	return userCtx, nil
}

// GetDatabaseConnection returns the Visma database connection for the user's current company
func (uc *UserContext) GetDatabaseConnection() (*vismadb.Client, error) {
	if uc.CompanyID == nil {
		return nil, fmt.Errorf("user %s has no company selected", uc.Username)
	}
	
	return uc.manager.GetConnectionForCompany(*uc.CompanyID)
}

// GetAccessibleCompanies returns the companies the user has access to
func (uc *UserContext) GetAccessibleCompanies() ([]int, error) {
	return uc.manager.GetUserAccessibleCompanies(uc.Username)
}

// SwitchCompany switches the user's current company context
func (uc *UserContext) SwitchCompany(companyID int) error {
	if err := uc.manager.SwitchUserCompany(uc.Username, companyID); err != nil {
		return err
	}
	
	// Update local context
	uc.CompanyID = &companyID
	return nil
}

// HasCompanyAccess checks if the user has access to a specific company
func (uc *UserContext) HasCompanyAccess(companyID int) (bool, error) {
	accessibleCompanies, err := uc.GetAccessibleCompanies()
	if err != nil {
		return false, err
	}
	
	for _, accessibleCompanyID := range accessibleCompanies {
		if accessibleCompanyID == companyID {
			return true, nil
		}
	}
	
	return false, nil
}

// ConnectionMiddleware creates middleware that adds connection manager to request context
func ConnectionMiddleware(manager *ConnectionManager) func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// Add connection manager to context
			ctx := context.WithValue(r.Context(), ConnectionManagerKey, manager)
			
			// Get user context and add current company to context
			if userCtx, err := GetUserContextFromRequest(r, manager); err == nil {
				if userCtx.CompanyID != nil {
					ctx = context.WithValue(ctx, UserCompanyKey, *userCtx.CompanyID)
				}
			}
			
			next.ServeHTTP(w, r.WithContext(ctx))
		}
	}
}

// GetConnectionManagerFromContext retrieves the connection manager from request context
func GetConnectionManagerFromContext(ctx context.Context) (*ConnectionManager, error) {
	if manager, ok := ctx.Value(ConnectionManagerKey).(*ConnectionManager); ok {
		return manager, nil
	}
	return nil, fmt.Errorf("connection manager not found in context")
}

// GetUserCompanyFromContext retrieves the user's current company ID from request context
func GetUserCompanyFromContext(ctx context.Context) (int, error) {
	if companyID, ok := ctx.Value(UserCompanyKey).(int); ok {
		return companyID, nil
	}
	return 0, fmt.Errorf("user company not found in context")
}

// GetDatabaseConnectionFromRequest is a convenience function to get a database connection from an HTTP request
func GetDatabaseConnectionFromRequest(r *http.Request) (*vismadb.Client, error) {
	manager, err := GetConnectionManagerFromContext(r.Context())
	if err != nil {
		return nil, err
	}
	
	userCtx, err := GetUserContextFromRequest(r, manager)
	if err != nil {
		return nil, err
	}
	
	return userCtx.GetDatabaseConnection()
}
