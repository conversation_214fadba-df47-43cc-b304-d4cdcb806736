package connectionpool

import (
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
)

// ConnectionManager manages a pool of Visma database connections
type ConnectionManager struct {
	connections map[int]*vismadb.Client // companyID -> connection
	mutex       sync.RWMutex
	
	// Services for fetching connection configurations
	companyService    *services.CompanyService
	connectionService *services.DatabaseConnectionService
	sessionService    *services.UserSessionService
	
	// Configuration
	maxIdleTime time.Duration
	cleanupTicker *time.Ticker
	stopCleanup chan bool
}

// NewConnectionManager creates a new connection manager
func NewConnectionManager(
	companyService *services.CompanyService,
	connectionService *services.DatabaseConnectionService,
	sessionService *services.UserSessionService,
) *ConnectionManager {
	cm := &ConnectionManager{
		connections:       make(map[int]*vismadb.Client),
		companyService:    companyService,
		connectionService: connectionService,
		sessionService:    sessionService,
		maxIdleTime:       30 * time.Minute, // Close idle connections after 30 minutes
		stopCleanup:       make(chan bool),
	}
	
	// Start cleanup routine
	cm.startCleanupRoutine()
	
	return cm
}

// GetConnectionForUser retrieves a Visma database connection for the user's current company
func (cm *ConnectionManager) GetConnectionForUser(username string) (*vismadb.Client, error) {
	if username == "" {
		return nil, fmt.Errorf("username is required")
	}
	
	// Get user's current session
	session, err := cm.sessionService.GetUserSession(username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user session: %w", err)
	}
	
	if session == nil {
		return nil, fmt.Errorf("user session not found for %s", username)
	}
	
	if session.CurrentCompanyID == nil {
		return nil, fmt.Errorf("user %s has no current company selected", username)
	}
	
	return cm.GetConnectionForCompany(*session.CurrentCompanyID)
}

// GetConnectionForCompany retrieves a Visma database connection for a specific company
func (cm *ConnectionManager) GetConnectionForCompany(companyID int) (*vismadb.Client, error) {
	if companyID <= 0 {
		return nil, fmt.Errorf("invalid company ID: %d", companyID)
	}
	
	cm.mutex.RLock()
	if conn, exists := cm.connections[companyID]; exists {
		cm.mutex.RUnlock()
		
		// Test connection to ensure it's still valid
		if err := cm.testConnection(conn); err == nil {
			slog.Debug("Reusing existing connection", "company_id", companyID)
			return conn, nil
		}
		
		// Connection is invalid, remove it and create a new one
		slog.Warn("Existing connection is invalid, removing", "company_id", companyID, "error", err)
		cm.mutex.Lock()
		delete(cm.connections, companyID)
		cm.mutex.Unlock()
	} else {
		cm.mutex.RUnlock()
	}
	
	// Create new connection
	return cm.createConnection(companyID)
}

// createConnection creates a new Visma database connection for a company
func (cm *ConnectionManager) createConnection(companyID int) (*vismadb.Client, error) {
	// Get database connection configuration
	dbConn, err := cm.connectionService.GetDatabaseConnectionByCompany(companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection config: %w", err)
	}
	
	if dbConn == nil {
		return nil, fmt.Errorf("no database connection configured for company %d", companyID)
	}
	
	if !dbConn.Enabled {
		return nil, fmt.Errorf("database connection for company %d is disabled", companyID)
	}
	
	// Create Visma client configuration
	config := &vismadb.ClientConfig{
		Server:   dbConn.Server,
		Instance: dbConn.Instance,
		Port:     dbConn.Port,
		Username: dbConn.Username,
		Password: dbConn.Password,
		Database: dbConn.DatabaseName,
		Params:   dbConn.Params,
	}
	
	// Create new Visma client
	client, err := vismadb.NewClient(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Visma client for company %d: %w", companyID, err)
	}
	
	// Store in connection pool
	cm.mutex.Lock()
	cm.connections[companyID] = client
	cm.mutex.Unlock()
	
	slog.Info("Created new database connection", "company_id", companyID, "server", dbConn.Server, "database", dbConn.DatabaseName)
	return client, nil
}

// testConnection tests if a connection is still valid
func (cm *ConnectionManager) testConnection(client *vismadb.Client) error {
	if client == nil || client.Conn == nil {
		return fmt.Errorf("connection is nil")
	}
	
	// Simple ping test
	return client.Conn.Ping()
}

// GetUserAccessibleCompanies returns the companies a user has access to
func (cm *ConnectionManager) GetUserAccessibleCompanies(username string) ([]int, error) {
	if username == "" {
		return nil, fmt.Errorf("username is required")
	}
	
	session, err := cm.sessionService.GetUserSession(username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user session: %w", err)
	}
	
	if session == nil {
		return []int{}, nil
	}
	
	return session.AccessibleCompanies, nil
}

// SwitchUserCompany switches a user's current company context
func (cm *ConnectionManager) SwitchUserCompany(username string, companyID int) error {
	if username == "" {
		return fmt.Errorf("username is required")
	}
	
	if companyID <= 0 {
		return fmt.Errorf("invalid company ID: %d", companyID)
	}
	
	// Verify user has access to the company
	accessibleCompanies, err := cm.GetUserAccessibleCompanies(username)
	if err != nil {
		return fmt.Errorf("failed to get user's accessible companies: %w", err)
	}
	
	hasAccess := false
	for _, accessibleCompanyID := range accessibleCompanies {
		if accessibleCompanyID == companyID {
			hasAccess = true
			break
		}
	}
	
	if !hasAccess {
		return fmt.Errorf("user %s does not have access to company %d", username, companyID)
	}
	
	// Update user's current company
	if err := cm.sessionService.UpdateCurrentCompany(username, companyID); err != nil {
		return fmt.Errorf("failed to update user's current company: %w", err)
	}
	
	slog.Info("User switched company", "username", username, "company_id", companyID)
	return nil
}

// CloseConnection closes a specific connection
func (cm *ConnectionManager) CloseConnection(companyID int) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	if conn, exists := cm.connections[companyID]; exists {
		conn.Close()
		delete(cm.connections, companyID)
		slog.Debug("Closed connection", "company_id", companyID)
	}
}

// CloseAllConnections closes all connections
func (cm *ConnectionManager) CloseAllConnections() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	for companyID, conn := range cm.connections {
		conn.Close()
		slog.Debug("Closed connection", "company_id", companyID)
	}
	
	cm.connections = make(map[int]*vismadb.Client)
	slog.Info("Closed all database connections")
}

// startCleanupRoutine starts a background routine to clean up idle connections
func (cm *ConnectionManager) startCleanupRoutine() {
	cm.cleanupTicker = time.NewTicker(10 * time.Minute) // Check every 10 minutes
	
	go func() {
		for {
			select {
			case <-cm.cleanupTicker.C:
				cm.cleanupIdleConnections()
			case <-cm.stopCleanup:
				cm.cleanupTicker.Stop()
				return
			}
		}
	}()
}

// cleanupIdleConnections removes idle or invalid connections
func (cm *ConnectionManager) cleanupIdleConnections() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	for companyID, conn := range cm.connections {
		if err := cm.testConnection(conn); err != nil {
			conn.Close()
			delete(cm.connections, companyID)
			slog.Debug("Removed invalid connection during cleanup", "company_id", companyID, "error", err)
		}
	}
}

// Stop stops the connection manager and closes all connections
func (cm *ConnectionManager) Stop() {
	close(cm.stopCleanup)
	cm.CloseAllConnections()
	slog.Info("Connection manager stopped")
}
