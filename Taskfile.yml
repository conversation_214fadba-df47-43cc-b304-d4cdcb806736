version: "3"

vars:
  APPNAME: erv-go-lonn-exporter
  GOARCH: amd64
  CGO: "0" # Default CGO setting, can be overridden with CGO=0 task build:linux
  PROJECT_DIR:
    sh: pwd
  BUILD_DIR: "{{.PROJECT_DIR}}/bin"
  VERSION:
    sh: git describe --tags --always
  COMMIT:
    sh: git rev-parse HEAD
  BRANCH:
    sh: git rev-parse --abbrev-ref HEAD
  BUILD:
    sh: date +%FT%T%z
  GOVERSION:
    sh: go version | awk '{print $3}'
  LDFLAGS: >
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.Version={{.VERSION}}
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.Commit={{.COMMIT}}
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.Branch={{.BRANCH}}
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.Build={{.BUILD}}
    -X github.com/probits-as/erv-go-lonn-exporter/internal/app.GoVersion={{.GOVERSION}}

tasks:
  build:windows:
    desc: Build for Windows
    cmds:
      - task: ensure-build-dir
      - CGO_ENABLED={{.CGO}} GOOS=windows GOARCH=amd64 go build -ldflags "{{.LDFLAGS}}" -o {{.BUILD_DIR}}/{{.APPNAME}}-windows.exe .

  build:linux:
    desc: Build for Linux
    cmds:
      - task: ensure-build-dir
      - CGO_ENABLED={{.CGO}} GOOS=linux go build -ldflags "{{.LDFLAGS}}" -o {{.BUILD_DIR}}/{{.APPNAME}}-linux-{{.GOARCH}} .

  build:darwin-amd64:
    desc: Build for macOS (Intel)
    cmds:
      - task: ensure-build-dir
      - CGO_ENABLED={{.CGO}} GOOS=darwin GOARCH=amd64 go build -ldflags "{{.LDFLAGS}}" -o {{.BUILD_DIR}}/{{.APPNAME}}-darwin-amd64 .

  build:darwin-arm64:
    desc: Build for macOS (Apple Silicon)
    cmds:
      - task: ensure-build-dir
      - CGO_ENABLED={{.CGO}} GOOS=darwin GOARCH=arm64 go build -ldflags "{{.LDFLAGS}}" -o {{.BUILD_DIR}}/{{.APPNAME}}-darwin-arm64 .

  ensure-build-dir:
    internal: true
    cmds:
      - mkdir -p {{.BUILD_DIR}}

  docker:
    desc: Build docker image
    cmds:
      - >
        docker build 
        --build-arg VERSION={{.VERSION}}
        --build-arg COMMIT={{.COMMIT}}
        --build-arg BRANCH={{.BRANCH}}
        --build-arg BUILD={{.BUILD}}
        --build-arg GOVERSION={{.GOVERSION}}
        -t cr.pbtech.no/erv/{{.APPNAME}}:{{.VERSION}}
        -f docker/Dockerfile .

  clean:
    desc: Clean build directory and files
    cmds:
      - rm -rf {{.BUILD_DIR}}
      - rm -rf public/openapi.json
