# Keycloak Web Integration Guide

This guide explains how to use the web-based Keycloak authentication integration in ERV Go Lonn Exporter.

## Overview

The application now supports two authentication modes:

1. **Web Browser Authentication** - OAuth2/OIDC flow for web users
2. **API Authentication** - JWT Bearer tokens for API access

## Web Browser Authentication Flow

### Login Process

1. Users accessing the application are automatically redirected to Keycloak login page
2. After successful authentication, users are redirected back to the application
3. Session cookies are used to maintain authentication state
4. Users see their username in the navbar with a logout button

### Logout Process

1. Users can click the "Logg ut" button in the navbar
2. This clears the local session and redirects to Keycloak logout
3. After Keycloak logout, users are redirected back to the application

### Session Management

- Sessions are encrypted using configurable secret keys
- Session duration is configurable (default: 1 hour)
- Sessions automatically expire and redirect to login
- Sessions persist across browser tabs but not browser restarts
- JWT tokens are stored server-side to avoid cookie size limits
- Automatic cleanup of expired tokens prevents memory leaks

## Configuration

### Basic Keycloak Configuration

```toml
[keycloak]
# Enable Keycloak authentication
enabled = true

# URL to the Keycloak server
url = "https://your-keycloak-server/auth"

# Realm to use
realm = "your-realm"

# Client ID
client_id = "lonnexporter"

# Client secret
client_secret = "your-client-secret"

# Required role or group
required_role = "lonnexport-user"

# Redirect URL for OAuth2 callback (optional)
redirect_url = "https://your-domain.com/auth/callback"
```

### Session Configuration

```toml
[session]
# Session secret key for cookie encryption (leave empty to auto-generate)
secret_key = "your-32-character-secret-key"

# Session max age in seconds (default: 3600 = 1 hour)
max_age = 3600

# Use secure cookies (HTTPS only) - set to true in production
secure = true

# Use HTTP-only cookies (recommended for security)
http_only = true
```

## Keycloak Client Setup

1. **Create a new client** in your Keycloak realm:

   - Client ID: `lonnexporter`
   - Client Protocol: `openid-connect`
   - Access Type: `confidential`

2. **Configure Valid Redirect URIs**:

   - Add: `http://localhost:8080/auth/callback` (for development)
   - Add: `https://your-domain.com/auth/callback` (for production)

3. **Configure Valid Post Logout Redirect URIs**:

   - Add: `http://localhost:8080/` (for development)
   - Add: `https://your-domain.com/` (for production)

4. **Create roles and assign to users**:
   - Create a role named `lonnexport-user` (or whatever you configured)
   - Assign this role to users who should have access

## API Authentication

For API access, the application still supports JWT Bearer tokens:

```bash
# Get token from Keycloak
curl -X POST "https://your-keycloak-server/auth/realms/your-realm/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=your-client-id" \
  -d "client_secret=your-client-secret"

# Use token in API requests
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8080/employees"
```

## Testing

### Web Browser Testing

1. Start your application with Keycloak enabled
2. Open a web browser and navigate to your application
3. You should be redirected to Keycloak for authentication
4. After successful authentication, you should be redirected back to the application
5. You should see your username in the navbar with a logout button

### API Testing

1. Obtain a JWT token from Keycloak
2. Use the token in the Authorization header for API requests
3. API requests without valid tokens will return 401 Unauthorized

## Troubleshooting

### Common Issues

**Redirect URI Mismatch**

- Ensure the redirect URL in Keycloak client configuration matches your application's callback URL
- Check both development and production URLs

**Session Issues**

- Check that session secret key is properly configured and consistent across restarts
- Verify session max age is appropriate for your use case

**HTTPS in Production**

- Set `session.secure = true` when using HTTPS in production
- Ensure your domain has valid SSL certificates

**Token Validation Errors**

- Ensure your Keycloak server is accessible from your application
- Check network connectivity and firewall rules

**Access Denied**

- Verify that the user has the required role assigned in Keycloak
- Check the role name matches the configuration

**Client Configuration**

- Check that the client configuration in Keycloak matches your application configuration
- Verify client secret is correct

### Debugging

Check the application logs for detailed error messages:

```bash
# Run with debug logging
./lonnexporter run --log-level debug
```

Common log messages:

- `User authenticated` - Successful login
- `Token validation failed` - Invalid or expired token
- `Insufficient permissions` - User lacks required role
- `Missing Authorization header` - API request without token
