# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "gomod" # See documentation for possible values
    directory: "/" # Location of package manifests
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Europe/Oslo"

    # Batch updates together
    groups:
      golang-dependencies:
        patterns:
          - "golang.org/x/*"
      core-dependencies:
        patterns:
          - "github.com/spf13/*"

    # Additional settings
    open-pull-requests-limit: 10
    pull-request-branch-name:
      separator: "-"
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
    labels:
      - "dependencies"

    # Ignore certain updates
    ignore:
      # Ignore patches for production dependencies
      - dependency-name: "*"
        update-types: ["version-update:semver-patch"]
