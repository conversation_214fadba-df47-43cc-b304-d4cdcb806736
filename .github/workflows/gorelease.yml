name: Build and release

on:
  push:
    tags:
      - "v*"

jobs:
  build:
    name: Create erv-go-lonn-exporter image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setting the docker metas
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: cr.pbtech.no/erv/lonnexporter

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
        with:
          buildkitd-flags: --debug

      - name: Log in to the Container registry
        uses: docker/login-action@v1
        with:
          registry: cr.pbtech.no
          username: ${{ secrets.CR_USERNAME }}
          password: ${{ secrets.CR_PASSWORD }}

      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.24

      - name: Get branch name
        id: get-branch
        run: |
          branch=${GITHUB_REF#refs/*/}
          echo "branch=$branch" >> $GITHUB_OUTPUT
        shell: bash

      - name: Get commit hash
        id: get-commit
        run: echo "commit=${GITHUB_SHA}" >> $GITHUB_OUTPUT
        shell: bash

      - name: Get current date
        id: get-date
        run: echo "date=$(date +'%FT%T%z')" >> $GITHUB_OUTPUT
        shell: bash

      - name: Get go version
        id: go-version
        run: echo "GOVERSION=$(go version | awk '{print $3}')" >> $GITHUB_OUTPUT
        shell: bash

      - name: Build and push image
        uses: docker/build-push-action@v3
        with:
          push: true
          context: .
          file: docker/Dockerfile
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            VERSION=${{ steps.get-branch.outputs.branch }}
            COMMIT=${{ steps.get-commit.outputs.commit }}
            BRANCH=${{ steps.get-branch.outputs.branch }}
            BUILD=${{ steps.get-date.outputs.date }}
            GOVERSION=${{ steps.go-version.outputs.GOVERSION }}
