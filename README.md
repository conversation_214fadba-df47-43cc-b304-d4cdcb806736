# ERV Lønn Exporter

A multi-tenant Go application for exporting salary data from Visma Business to file format. The application provides a web interface for selecting and exporting salary data with various filtering options, supporting multiple companies with individual Keycloak realms and databases.

## Features

### Core Features

- Web-based interface for selecting and exporting salary data
- Support for both hourly and monthly salaries
- Employee search and filtering
- Date range selection
- Export to CSV in VLSTANDARD format
- **Server-side user settings and form data persistence**
- **SQLite database for user preferences and session storage**
- **Per-user form data storage and retrieval**

### Multi-Tenant Architecture

- **Single application instance serving multiple companies**
- **Company-specific Keycloak realms and database connections**
- **Dynamic company switching for users with multi-company access**
- **Claims-based access control via JWT custom claims**
- **Company selection landing page**
- **Isolated data and settings per company**

### Authentication & Security

- Keycloak authentication with web-based login flow
- **Enhanced session management with database storage**
- API authentication with JWT Bearer tokens
- Basic authentication fallback
- **Multi-company access control**

## Requirements

- Go 1.23 or later
- Access to a Visma Business database (MSSQL)
- Windows or Linux environment
- SQLite support (included with Go build)
- Task (optional, for using taskfile commands)

## Authentication

The application supports multiple authentication methods:

### Keycloak Authentication (Recommended)

For web-based authentication with OAuth2/OIDC flow:

1. Configure Keycloak settings in `conf/app.toml`:

   ```toml
   [keycloak]
   enabled = true
   url = "https://your-keycloak-server/auth"
   realm = "your-realm"
   client_id = "lonnexporter"
   client_secret = "your-client-secret"
   required_role = "lonnexport-user"
   ```

2. Users will be redirected to Keycloak for login
3. After authentication, users are redirected back with a session cookie
4. Sessions are managed automatically with configurable expiration

### API Authentication

For programmatic access, use JWT Bearer tokens:

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8080/employees
```

### Basic Authentication (Fallback)

When Keycloak is disabled, basic authentication is used:

```toml
[http]
username = "admin"
password = "admin"
```

For detailed Keycloak setup instructions, see [docs/keycloak-web-integration.md](docs/keycloak-web-integration.md).

## Multi-Tenant Setup

The application supports multiple companies with individual Keycloak realms and Visma databases in a single instance.

### Architecture Overview

- **Companies**: Each company has its own Keycloak configuration and Visma database connection
- **User Access**: Users can access multiple companies based on JWT custom claims
- **Dynamic Switching**: Authenticated users can switch between accessible companies without re-authentication
- **Data Isolation**: Each company's data is completely isolated

### Authentication Flow

1. **Company Selection** → User visits root URL (`/`) and selects a company
2. **Keycloak Login** → Redirects to company-specific Keycloak realm
3. **JWT Processing** → Extracts accessible companies from custom claims
4. **Session Management** → Stores user context and accessible companies
5. **Company Switching** → Users can switch via dropdown in main interface

### Setup for New Installations

1. **Configure basic settings** in `conf/app.toml`:

   ```toml
   [database]
   path = "data/app.db"

   [session]
   secret = "your-session-secret"
   max_age = 3600
   ```

2. **Create companies** using CLI commands:

   ```bash
   # Create a company
   ./erv-go-lonn-exporter company create \
     --name="company1" \
     --display-name="Company One AS" \
     --keycloak-url="https://keycloak.company1.com" \
     --keycloak-realm="company1" \
     --keycloak-client-id="lonnexporter" \
     --keycloak-client-secret="secret" \
     --keycloak-required-role="lonnexport"
   ```

3. **Create database connections**:

   ```bash
   # Create database connection for company (use company ID from previous step)
   ./erv-go-lonn-exporter database create 1 \
     --server="sql.company1.com" \
     --port=1433 \
     --username="dbuser" \
     --password="dbpass" \
     --database="VismaDB1"
   ```

4. **Configure Keycloak custom claims** to include accessible companies:
   ```json
   {
     "accessible_companies": [1, 2, 3],
     "preferred_username": "<EMAIL>"
   }
   ```

### Migration from Single-Tenant

If you have an existing single-tenant installation:

```bash
# Preview what will be migrated
./erv-go-lonn-exporter migrate --dry-run

# Perform migration
./erv-go-lonn-exporter migrate
```

This creates a default company from your existing configuration.

### Management Commands

```bash
# List companies
./erv-go-lonn-exporter company list

# List database connections
./erv-go-lonn-exporter database list

# Update company
./erv-go-lonn-exporter company update 1 --display-name="New Name"

# Create additional companies
./erv-go-lonn-exporter company create --name="company2" ...
```

### JWT Custom Claims

Configure your Keycloak realms to include custom claims in JWT tokens:

- **accessible_companies**: Array of company IDs the user can access
- **companies**: Alternative claim name (also supported)
- **groups**: Group-based access (e.g., `/company_1`, `/company_2`)

Example JWT payload:

```json
{
  "sub": "user-id",
  "preferred_username": "<EMAIL>",
  "accessible_companies": [1, 2],
  "realm_access": {
    "roles": ["lonnexport"]
  }
}
```

## Database and User Settings

The application uses SQLite for storing user settings, form data preferences, and session information. This provides:

- **Per-user form data persistence**: Each authenticated user's form preferences are saved separately
- **Session storage**: Enhanced session management with database-backed storage
- **User settings**: Configurable user preferences and application settings
- **Automatic cleanup**: Expired sessions are automatically removed

### Database Configuration

Configure the SQLite database in `conf/app.toml`:

```toml
[database]
# SQLite database path for storing user settings and sessions
path = "data/app.db"

# Maximum number of open connections to the database
max_open_conns = 10

# Maximum number of idle connections in the pool
max_idle_conns = 5
```

### Database Features

- **Automatic initialization**: Database and tables are created automatically on first run
- **Migration support**: Schema updates are handled automatically
- **Connection pooling**: Configurable connection limits for optimal performance
- **WAL mode**: Uses Write-Ahead Logging for better concurrent access
- **Foreign key support**: Ensures data integrity

### User Data Storage

When using Keycloak authentication, the application stores:

- **Form preferences**: Date ranges, selected employees, salary types, export format
- **User settings**: Application preferences and configuration
- **Session data**: Authentication tokens and session information

All data is associated with the user's Keycloak username and isolated between users.

> **Note**: Previous versions stored form data in browser localStorage. The application now uses server-side storage, providing better data persistence and user-specific settings. Existing localStorage data will not be automatically migrated.

### API Endpoints

The application provides REST API endpoints for managing user settings (requires authentication):

#### Form Data Management

- `GET /api/settings/form-data` - Retrieve user's form data preferences
- `POST /api/settings/form-data` - Save user's form data preferences
- `DELETE /api/settings/form-data` - Delete user's form data preferences

#### User Settings Management

- `GET /api/settings/{key}` - Retrieve a specific user setting
- `POST /api/settings/{key}` - Set a specific user setting
- `DELETE /api/settings/{key}` - Delete a specific user setting

Example API usage:

```bash
# Get form data (requires authentication)
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8080/api/settings/form-data

# Save form data
curl -X POST \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"toDate":"31-12-2023","fromDate":"01-01-2023","showExported":true}' \
  http://localhost:8080/api/settings/form-data

# Set a user setting
curl -X POST \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"value":"dark"}' \
  http://localhost:8080/api/settings/theme
```

## Development Setup

1. Clone the repository:

   ```bash
   git clone https://github.com/probits-as/erv-go-lonn-exporter.git
   cd erv-go-lonn-exporter
   ```

2. Install dependencies:

   ```bash
   go mod download
   ```

3. Configure the application using either:

   A. Environment variables (Prefix with LONNEXPORTER):

   ```bash
   export LONNEXPORTER_HTTP_PORT=8080
   export LONNEXPORTER_HTTP_USERNAME=admin
   export LONNEXPORTER_HTTP_PASSWORD=admin
   export LONNEXPORTER_VISMA_SERVER=your-server
   export LONNEXPORTER_VISMA_INSTANCE=your-instance
   export LONNEXPORTER_VISMA_PORT=1433
   export LONNEXPORTER_VISMA_USERNAME=your-username
   export LONNEXPORTER_VISMA_PASSWORD=your-password
   export LONNEXPORTER_VISMA_DATABASE=your-database
   export LONNEXPORTER_VISMA_COMPANYNAME="Your Company AS"
   export LONNEXPORTER_EXPORT_FILE=export.txt
   export LONNEXPORTER_EXPORT_OVERWRITE=false
   export LONNEXPORTER_LOGGING_LEVEL=info
   export LONNEXPORTER_LOGGING_FORMAT=pretty
   ```

   B. Configuration file (optional) - Create `lonnexporter.yaml` and place it with the binary:

   ```yaml
   http:
     port: 8080
     username: "admin" # Basic auth username
     password: "admin" # Basic auth password

   database:
     path: "data/app.db"
     max_open_conns: 10
     max_idle_conns: 5

   visma:
     server: "your-server"
     instance: "your-instance"
     port: 1433
     username: "your-username"
     password: "your-password"
     database: "your-database"
     companyname: "Your Company AS"
     params:
       dial timeout: "30"

   export:
     file: "export.txt"
     overwrite: false

   logging:
     level: "info" # debug, info, warn, error
     format: "pretty" # json, text, pretty
   ```

4. Run the application in development mode:
   ```bash
   go run main.go run
   ```

## Building

The project uses Taskfile for build automation. Install Task from [taskfile.dev](https://taskfile.dev) if you haven't already.

### Available Task Commands

```bash
# List all available tasks
task --list

# Build for current platform
task build

# Build for Windows
task build:windows

# Build for Linux
task build:linux

# Clean build artifacts
task clean

# Run tests
task test
```

### Manual Building (without Task)

Local Build:

```bash
go build -o bin/erv-go-lonn-exporter
```

Cross-Platform Build:

```bash
# Windows
GOOS=windows GOARCH=amd64 go build -o bin/erv-go-lonn-exporter.exe

# Linux
GOOS=linux GOARCH=amd64 go build -o bin/erv-go-lonn-exporter
```

## Usage

1. Start the server:

   ```bash
   ./erv-go-lonn-exporter run
   ```

2. Access the web interface at `http://localhost:8080`

3. Log in using the configured username and password

4. Select the desired options:

   - Date range
   - Employees
   - Salary types
   - Export options (hourly/monthly salaries)

5. Click "Export" to generate the file

## Running as a Windows Service

To run the application as a Windows service, we recommend using [NSSM (Non-Sucking Service Manager)](https://nssm.cc/). Here's how to set it up:

1. Download and install NSSM from https://nssm.cc/download

2. Open Command Prompt as Administrator and navigate to where you installed NSSM:

```cmd
cd C:\path\to\nssm\win64
```

3. Install the service using NSSM:

```cmd
nssm install lonnexporter
```

4. In the NSSM service installer:

   - Path: Browse to your `erv-go-lonn-exporter.exe`
   - Startup directory: Set to the directory containing the executable
   - Arguments: `run`

5. Set the service description (optional):

```cmd
nssm set lonnexporter Description "Visma Business lønn exporter"
```

6. Start the service:

```cmd
nssm start lonnexporter
```

Other useful NSSM commands:

- Stop the service: `nssm stop lonnexporter`
- Remove the service: `nssm remove lonnexporter`
- Edit service configuration: `nssm edit lonnexporter`
- Check service status: `nssm status lonnexporter`

The service will automatically start when Windows boots and can be managed through Windows Services (services.msc).

## Configuration Options

The application can be configured in multiple ways, with the following priority order:

1. Command-line flags (highest priority)
2. Environment variables
3. Configuration file (lowest priority, entirely optional)

You can run the application with just command-line flags, just environment variables, or any combination. The configuration file is completely optional and will be used only if present.

### A. Command-line Flags (Recommended for testing/development)

```bash
./erv-go-lonn-exporter run \
  --http-port=8080 \
  --visma-server=your-server \
  --visma-database=your-database
```

### B. Environment Variables (Recommended for production)

```bash
export LONNEXPORTER_HTTP_PORT=8080
export LONNEXPORTER_VISMA_SERVER=your-server
export LONNEXPORTER_VISMA_DATABASE=your-database
./erv-go-lonn-exporter run
```

### C. Configuration File (Optional)

If you prefer using a configuration file, create `lonnexporter.yaml` in the same directory as the binary. The application will automatically detect and use it if present, but will work fine without it.

```yaml
http:
  port: 8080
  username: "admin"
database:
  path: "data/app.db"
  max_open_conns: 10
  max_idle_conns: 5
visma:
  server: "your-server"
  database: "your-database"
```

### Command-line Flags

```bash
# HTTP Server Settings
--http-listen string     The address to listen on for HTTP-traffic (default "0.0.0.0")
--http-port int          The port to listen on for HTTP-traffic (default 8080)
--http-username string   The username to use for HTTP-traffic (default "admin")
--http-password string   The password to use for HTTP-traffic (default "admin")

# Visma Database Settings
--visma-server string    The server to use for Visma
--visma-instance string  The instance to use for Visma
--visma-username string  The username to use for Visma
--visma-password string  The password to use for Visma
--visma-companyname string The company name to use for Visma (default "ERV Teknikk AS")
--visma-database string  The database to use for Visma (default "F0001")
--visma-port int        The port to use for Visma (default 1433)

# Database Settings
--database-path string           Path to SQLite database file (default "data/app.db")
--database-max-open-conns int    Maximum number of open database connections (default 10)
--database-max-idle-conns int    Maximum number of idle database connections (default 5)

# Export Settings
--export-layout string   The layout to use for export (default "VLSTANDARD")
--export-file string     The file to save export to (default "./go-lonn-exporter.csv")
--export-overwrite      Overwrite the export file if it exists
```

### Configuration Table

| Option                    | Flag                        | Environment Variable      | Default              | Description           |
| ------------------------- | --------------------------- | ------------------------- | -------------------- | --------------------- |
| `http.listen`             | `--http-listen`             | `HTTP_LISTEN`             | 0.0.0.0              | HTTP listen address   |
| `http.port`               | `--http-port`               | `HTTP_PORT`               | 8080                 | Web server port       |
| `http.username`           | `--http-username`           | `HTTP_USERNAME`           | admin                | Basic auth username   |
| `http.password`           | `--http-password`           | `HTTP_PASSWORD`           | admin                | Basic auth password   |
| `visma.server`            | `--visma-server`            | `VISMA_SERVER`            | -                    | Visma database server |
| `visma.instance`          | `--visma-instance`          | `VISMA_INSTANCE`          | -                    | Visma server instance |
| `visma.username`          | `--visma-username`          | `VISMA_USERNAME`          | -                    | Visma username        |
| `visma.password`          | `--visma-password`          | `VISMA_PASSWORD`          | -                    | Visma password        |
| `visma.companyname`       | `--visma-companyname`       | `VISMA_COMPANYNAME`       | ERV Teknikk AS       | Company name          |
| `visma.database`          | `--visma-database`          | `VISMA_DATABASE`          | F0001                | Database name         |
| `visma.port`              | `--visma-port`              | `VISMA_PORT`              | 1433                 | Database port         |
| `database.path`           | `--database-path`           | `DATABASE_PATH`           | data/app.db          | SQLite database path  |
| `database.max_open_conns` | `--database-max-open-conns` | `DATABASE_MAX_OPEN_CONNS` | 10                   | Max open connections  |
| `database.max_idle_conns` | `--database-max-idle-conns` | `DATABASE_MAX_IDLE_CONNS` | 5                    | Max idle connections  |
| `export.layout`           | `--export-layout`           | `EXPORT_LAYOUT`           | VLSTANDARD           | Export layout format  |
| `export.file`             | `--export-file`             | `EXPORT_FILE`             | go-lonn-exporter.csv | Export file path      |
| `export.overwrite`        | `--export-overwrite`        | `EXPORT_OVERWRITE`        | false                | Allow file overwrite  |

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'feat: Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Conventional Commits

We use [Conventional Commits](https://www.conventionalcommits.org/) for commit messages to automate versioning and changelog generation. Your commit messages should follow this format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

Types:

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools

Examples:

```bash
feat: add export to Excel format
fix(export): correct date format in CSV output
docs: update configuration documentation
chore(deps): update golang.org/x/sys
```

Breaking changes should be indicated by a `!` after the type/scope:

```bash
feat!: change export file format
```

This commit convention ensures:

1. Automatic versioning based on semantic versioning
2. Well-structured, automatically generated changelogs
3. Clear communication of change intent
4. Easier maintenance and review processes

## Release Workflow

This project uses [GoReleaser](https://goreleaser.com/) for automated releases through GitHub Actions. The workflow:

1. Automatically builds binaries for multiple platforms
2. Creates GitHub releases
3. Generates changelogs
4. Creates Docker images
5. Updates version information

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## Support

For support, please contact [<EMAIL>](mailto:<EMAIL>)
