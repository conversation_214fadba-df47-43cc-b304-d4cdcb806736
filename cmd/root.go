package cmd

import (
	"fmt"
	"log"
	"log/slog"
	"os"
	"path/filepath"
	"strings"

	"github.com/probits-as/erv-go-lonn-exporter/internal/app"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/prettylog"

	"github.com/fsnotify/fsnotify"
	"github.com/kardianos/osext"
	"github.com/pkg/errors"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var cfgFile string

var rootCmd = &cobra.Command{
	Use:   app.ApplicationName,
	Short: "Lønn exporter fra Visma Business til fil",
	Long:  `Lønn exporter fra Visma Business til angitt fil-format`,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	// Set build-information for this application (from vars.go) to viper
	for k, v := range map[string]string{
		"version":   app.Version,
		"branch":    app.Branch,
		"commit":    app.Commit,
		"build":     app.Build,
		"goVersion": app.GoVersion,
	} {
		viper.Set(k, v)
	}

	// Print the application name
	fmt.Println(app.Title)

	if err := rootCmd.Execute(); err != nil {
		slog.Error("error executing command", "error", err)
		os.Exit(1)
	}
}

// initConfig reads in config file and ENV variables if set.
func initConfig() {
	// Setup the logger and log-level
	lvl := new(slog.LevelVar)
	switch strings.ToLower(viper.GetString("logging.level")) {
	case "debug":
		lvl.Set(slog.LevelDebug)
	case "info":
		lvl.Set(slog.LevelInfo)
	case "warn":
		lvl.Set(slog.LevelWarn)
	default:
		lvl.Set(slog.LevelInfo)
	}

	debug := strings.ToLower(viper.GetString("logging.level")) == "debug"
	switch strings.ToLower(viper.GetString("logging.format")) {
	case "json":
		slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{AddSource: debug, Level: lvl})))
	case "text":
		slog.SetDefault(slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{AddSource: debug, Level: lvl})))
	case "pretty":
		slog.SetDefault(slog.New(prettylog.NewHandler(&slog.HandlerOptions{AddSource: debug, Level: lvl})))
	default:
		slog.SetDefault(slog.New(prettylog.NewHandler(&slog.HandlerOptions{AddSource: debug, Level: lvl})))
	}

	// Gets the directory this application is launched
	appDir, err := osext.ExecutableFolder()
	if err != nil {
		panic(errors.Wrap(err, "unable to get the location we where launched from"))
	}
	viper.Set("appDir", filepath.FromSlash(appDir))

	if cfgFile != "" {
		// Use config file from the flag.
		viper.SetConfigFile(cfgFile)
	} else {
		// Search for config-file at same location as the application.
		viper.SetConfigName("lonnexporter")             // name of config file (without extension)
		viper.AddConfigPath(fmt.Sprintf("%s/", appDir)) // call multiple times to add many search paths
	}

	// Reading in the config-file if found
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			log.Fatalf("fatal error reading config file: %s", err)
		}
	}

	// If config-file is used log this and watch the config-file for changes
	if viper.ConfigFileUsed() != "" {
		slog.Info("loaded config-file", "file", viper.ConfigFileUsed())
		viper.WatchConfig()
	}

	// Add enviroment variables which overrides any entries from config-file or defaults
	viper.SetEnvPrefix("LONNEXPORTER")                     // prefix ENV-variables we are looking for (export LONNEXPORTER_XXX=xxx)
	viper.AutomaticEnv()                                   // read in environment variables that match
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_")) // replaces _ in viper-keys to a . when using env, ex LONNEXPORTER_LEVEL1_LEVEL2 = LEVEL1.LEVEL2

	// Monitor for changes in the config file
	viper.OnConfigChange(func(e fsnotify.Event) {
		if e.Op == fsnotify.Write {
			slog.Info("config file changed, reloading configuration...")

			// Update logging configuration
			lvl := new(slog.LevelVar)
			switch strings.ToLower(viper.GetString("logging.level")) {
			case "debug":
				lvl.Set(slog.LevelDebug)
			case "info":
				lvl.Set(slog.LevelInfo)
			case "warn":
				lvl.Set(slog.LevelWarn)
			default:
				lvl.Set(slog.LevelInfo)
			}

			debug := strings.ToLower(viper.GetString("logging.level")) == "debug"
			switch strings.ToLower(viper.GetString("logging.format")) {
			case "json":
				slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{AddSource: debug, Level: lvl})))
			case "text":
				slog.SetDefault(slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{AddSource: debug, Level: lvl})))
			case "pretty":
				slog.SetDefault(slog.New(prettylog.NewHandler(&slog.HandlerOptions{AddSource: debug, Level: lvl})))
			default:
				slog.SetDefault(slog.New(prettylog.NewHandler(&slog.HandlerOptions{AddSource: debug, Level: lvl})))
			}

			// If Visma database configuration has changed, reconnect
			if client, ok := viper.Get("visma.client").(*vismadb.Client); ok {
				newConfig := &vismadb.ClientConfig{
					Server:   viper.GetString("visma.server"),
					Instance: viper.GetString("visma.instance"),
					Port:     viper.GetInt("visma.port"),
					Username: viper.GetString("visma.username"),
					Password: viper.GetString("visma.password"),
					Database: viper.GetString("visma.database"),
					Params:   viper.GetStringMapString("visma.params"),
				}

				// Check if any database configuration has changed
				if client.Server != newConfig.Server ||
					client.Instance != newConfig.Instance ||
					client.Port != newConfig.Port ||
					client.User != newConfig.Username ||
					client.Password != newConfig.Password ||
					client.Database != newConfig.Database {

					slog.Info("database configuration changed, reconnecting...")

					// Close existing connection
					client.Close()

					// Create new connection
					newClient, err := vismadb.NewClient(newConfig)
					if err != nil {
						slog.Error("error reconnecting to database", "error", err)
						return
					}

					// Update the client in viper
					viper.Set("visma.client", newClient)
					slog.Info("successfully reconnected to database")
				}
			}

			slog.Info("configuration reload complete")
		}
	})
}

func init() {
	cobra.OnInitialize(initConfig)

	/* -- Global flags -- */
	/* -- Config file settings -- */
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is $APPDIR/conf/app.toml)")
	/* -- Logging flags -- */
	rootCmd.PersistentFlags().String("log-level", "info", "Set the log-level (debug, info, warn, error, fatal, panic).")
	viper.BindPFlag("logging.level", rootCmd.PersistentFlags().Lookup("log-level"))
	rootCmd.PersistentFlags().String("log-format", "pretty", "Set the log-format (json, text, pretty).")
	viper.BindPFlag("logging.format", rootCmd.PersistentFlags().Lookup("log-format"))
	rootCmd.PersistentFlags().String("log-output", "stdout", "Set the log-output (stdout, stderr, file).")
	viper.BindPFlag("logging.output", rootCmd.PersistentFlags().Lookup("log-output"))
	rootCmd.PersistentFlags().String("log-file", "lonnexporter.log", "Set the log-file (if log-output is file).")
	viper.BindPFlag("logging.file", rootCmd.PersistentFlags().Lookup("log-file"))

}
