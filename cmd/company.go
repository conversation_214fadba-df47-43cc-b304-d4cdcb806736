package cmd

import (
	"fmt"
	"log/slog"
	"os"
	"strconv"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// companyCmd represents the company command
var companyCmd = &cobra.Command{
	Use:   "company",
	Short: "Manage companies and their configurations",
	Long:  `Manage companies and their Keycloak configurations for multi-tenant setup`,
}

// companyListCmd lists all companies
var companyListCmd = &cobra.Command{
	Use:   "list",
	Short: "List all companies",
	Long:  `List all companies and their configurations`,
	Run: func(cmd *cobra.Command, args []string) {
		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		companyService := services.NewCompanyService(repo)
		companies, err := companyService.GetCompanies()
		if err != nil {
			slog.Error("Failed to get companies", "error", err)
			os.Exit(1)
		}

		if len(companies) == 0 {
			fmt.Println("No companies found.")
			return
		}

		fmt.Printf("%-5s %-20s %-30s %-50s %-20s %-10s\n", "ID", "Name", "Display Name", "Keycloak URL", "Realm", "Enabled")
		fmt.Println("---------------------------------------------------------------------------------------------------------------------------")
		for _, company := range companies {
			enabled := "Yes"
			if !company.Enabled {
				enabled = "No"
			}
			fmt.Printf("%-5d %-20s %-30s %-50s %-20s %-10s\n",
				company.ID, company.Name, company.DisplayName,
				company.KeycloakURL, company.KeycloakRealm, enabled)
		}
	},
}

// companyCreateCmd creates a new company
var companyCreateCmd = &cobra.Command{
	Use:   "create",
	Short: "Create a new company",
	Long:  `Create a new company with Keycloak configuration`,
	Run: func(cmd *cobra.Command, args []string) {
		name, _ := cmd.Flags().GetString("name")
		displayName, _ := cmd.Flags().GetString("display-name")
		keycloakURL, _ := cmd.Flags().GetString("keycloak-url")
		keycloakRealm, _ := cmd.Flags().GetString("keycloak-realm")
		keycloakClientID, _ := cmd.Flags().GetString("keycloak-client-id")
		keycloakClientSecret, _ := cmd.Flags().GetString("keycloak-client-secret")
		keycloakRequiredRole, _ := cmd.Flags().GetString("keycloak-required-role")
		enabled, _ := cmd.Flags().GetBool("enabled")

		if name == "" || displayName == "" || keycloakURL == "" || keycloakRealm == "" || keycloakClientID == "" {
			fmt.Println("Error: name, display-name, keycloak-url, keycloak-realm, and keycloak-client-id are required")
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		company := &database.Company{
			Name:                 name,
			DisplayName:          displayName,
			KeycloakURL:          keycloakURL,
			KeycloakRealm:        keycloakRealm,
			KeycloakClientID:     keycloakClientID,
			KeycloakClientSecret: keycloakClientSecret,
			KeycloakRequiredRole: keycloakRequiredRole,
			Enabled:              enabled,
		}

		companyService := services.NewCompanyService(repo)
		if err := companyService.CreateCompany(company); err != nil {
			slog.Error("Failed to create company", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Company created successfully with ID: %d\n", company.ID)
	},
}

// companyUpdateCmd updates an existing company
var companyUpdateCmd = &cobra.Command{
	Use:   "update [company-id]",
	Short: "Update an existing company",
	Long:  `Update an existing company's configuration`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		companyID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: invalid company ID: %s\n", args[0])
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		companyService := services.NewCompanyService(repo)
		company, err := companyService.GetCompany(companyID)
		if err != nil {
			slog.Error("Failed to get company", "error", err)
			os.Exit(1)
		}

		if company == nil {
			fmt.Printf("Error: company with ID %d not found\n", companyID)
			os.Exit(1)
		}

		// Update fields if provided
		if cmd.Flags().Changed("name") {
			name, _ := cmd.Flags().GetString("name")
			company.Name = name
		}
		if cmd.Flags().Changed("display-name") {
			displayName, _ := cmd.Flags().GetString("display-name")
			company.DisplayName = displayName
		}
		if cmd.Flags().Changed("keycloak-url") {
			keycloakURL, _ := cmd.Flags().GetString("keycloak-url")
			company.KeycloakURL = keycloakURL
		}
		if cmd.Flags().Changed("keycloak-realm") {
			keycloakRealm, _ := cmd.Flags().GetString("keycloak-realm")
			company.KeycloakRealm = keycloakRealm
		}
		if cmd.Flags().Changed("keycloak-client-id") {
			keycloakClientID, _ := cmd.Flags().GetString("keycloak-client-id")
			company.KeycloakClientID = keycloakClientID
		}
		if cmd.Flags().Changed("keycloak-client-secret") {
			keycloakClientSecret, _ := cmd.Flags().GetString("keycloak-client-secret")
			company.KeycloakClientSecret = keycloakClientSecret
		}
		if cmd.Flags().Changed("keycloak-required-role") {
			keycloakRequiredRole, _ := cmd.Flags().GetString("keycloak-required-role")
			company.KeycloakRequiredRole = keycloakRequiredRole
		}
		if cmd.Flags().Changed("enabled") {
			enabled, _ := cmd.Flags().GetBool("enabled")
			company.Enabled = enabled
		}

		if err := companyService.UpdateCompany(company); err != nil {
			slog.Error("Failed to update company", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Company %d updated successfully\n", companyID)
	},
}

// companyDeleteCmd deletes a company
var companyDeleteCmd = &cobra.Command{
	Use:   "delete [company-id]",
	Short: "Delete a company",
	Long:  `Delete a company and all its associated data`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		companyID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: invalid company ID: %s\n", args[0])
			os.Exit(1)
		}

		confirm, _ := cmd.Flags().GetBool("confirm")
		if !confirm {
			fmt.Printf("Error: use --confirm flag to confirm deletion of company %d\n", companyID)
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		companyService := services.NewCompanyService(repo)
		if err := companyService.DeleteCompany(companyID); err != nil {
			slog.Error("Failed to delete company", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Company %d deleted successfully\n", companyID)
	},
}

// initRepository initializes the database repository
func initRepository() (database.Repository, error) {
	dbPath := viper.GetString("database.path")
	if dbPath == "" {
		dbPath = "./data.db"
	}

	sqliteDB, err := database.NewSQLiteDB(dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	return database.NewSQLiteRepository(sqliteDB), nil
}

func init() {
	rootCmd.AddCommand(companyCmd)
	companyCmd.AddCommand(companyListCmd)
	companyCmd.AddCommand(companyCreateCmd)
	companyCmd.AddCommand(companyUpdateCmd)
	companyCmd.AddCommand(companyDeleteCmd)

	// Create command flags
	companyCreateCmd.Flags().String("name", "", "Company name (required)")
	companyCreateCmd.Flags().String("display-name", "", "Company display name (required)")
	companyCreateCmd.Flags().String("keycloak-url", "", "Keycloak URL (required)")
	companyCreateCmd.Flags().String("keycloak-realm", "", "Keycloak realm (required)")
	companyCreateCmd.Flags().String("keycloak-client-id", "", "Keycloak client ID (required)")
	companyCreateCmd.Flags().String("keycloak-client-secret", "", "Keycloak client secret")
	companyCreateCmd.Flags().String("keycloak-required-role", "lonnexport", "Required role for access")
	companyCreateCmd.Flags().Bool("enabled", true, "Enable the company")

	// Update command flags (same as create but optional)
	companyUpdateCmd.Flags().String("name", "", "Company name")
	companyUpdateCmd.Flags().String("display-name", "", "Company display name")
	companyUpdateCmd.Flags().String("keycloak-url", "", "Keycloak URL")
	companyUpdateCmd.Flags().String("keycloak-realm", "", "Keycloak realm")
	companyUpdateCmd.Flags().String("keycloak-client-id", "", "Keycloak client ID")
	companyUpdateCmd.Flags().String("keycloak-client-secret", "", "Keycloak client secret")
	companyUpdateCmd.Flags().String("keycloak-required-role", "", "Required role for access")
	companyUpdateCmd.Flags().Bool("enabled", true, "Enable the company")

	// Delete command flags
	companyDeleteCmd.Flags().Bool("confirm", false, "Confirm deletion")
}
