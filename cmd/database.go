package cmd

import (
	"fmt"
	"log/slog"
	"os"
	"strconv"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
	"github.com/spf13/cobra"
)

// databaseCmd represents the database command
var databaseCmd = &cobra.Command{
	Use:   "database",
	Short: "Manage database connections",
	Long:  `Manage Visma database connections for companies`,
}

// databaseListCmd lists all database connections
var databaseListCmd = &cobra.Command{
	Use:   "list [company-id]",
	Short: "List database connections",
	Long:  `List database connections for a specific company or all companies`,
	Run: func(cmd *cobra.Command, args []string) {
		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		connectionService := services.NewDatabaseConnectionService(repo)
		companyService := services.NewCompanyService(repo)

		if len(args) > 0 {
			// List connections for specific company
			companyID, err := strconv.Atoi(args[0])
			if err != nil {
				fmt.Printf("Error: invalid company ID: %s\n", args[0])
				os.Exit(1)
			}

			connection, err := connectionService.GetDatabaseConnectionByCompany(companyID)
			if err != nil {
				slog.Error("Failed to get database connection", "error", err)
				os.Exit(1)
			}

			if connection == nil {
				fmt.Printf("No database connection found for company %d\n", companyID)
				return
			}

			company, _ := companyService.GetCompany(companyID)
			companyName := fmt.Sprintf("Company %d", companyID)
			if company != nil {
				companyName = company.DisplayName
			}

			fmt.Printf("Database connection for %s:\n", companyName)
			fmt.Printf("  ID: %d\n", connection.ID)
			fmt.Printf("  Server: %s\n", connection.Server)
			fmt.Printf("  Instance: %s\n", connection.Instance)
			fmt.Printf("  Port: %d\n", connection.Port)
			fmt.Printf("  Username: %s\n", connection.Username)
			fmt.Printf("  Database: %s\n", connection.DatabaseName)
			fmt.Printf("  Enabled: %t\n", connection.Enabled)
		} else {
			// List all companies and their connections
			companies, err := companyService.GetCompanies()
			if err != nil {
				slog.Error("Failed to get companies", "error", err)
				os.Exit(1)
			}

			if len(companies) == 0 {
				fmt.Println("No companies found.")
				return
			}

			fmt.Printf("%-5s %-30s %-30s %-20s %-10s %-10s\n", "ID", "Company", "Server", "Database", "Port", "Enabled")
			fmt.Println("---------------------------------------------------------------------------------------------")

			for _, company := range companies {
				connection, err := connectionService.GetDatabaseConnectionByCompany(company.ID)
				if err != nil {
					continue
				}

				if connection != nil {
					enabled := "Yes"
					if !connection.Enabled {
						enabled = "No"
					}
					fmt.Printf("%-5d %-30s %-30s %-20s %-10d %-10s\n",
						connection.ID, company.DisplayName, connection.Server,
						connection.DatabaseName, connection.Port, enabled)
				} else {
					fmt.Printf("%-5s %-30s %-30s %-20s %-10s %-10s\n",
						"-", company.DisplayName, "No connection", "-", "-", "-")
				}
			}
		}
	},
}

// databaseCreateCmd creates a new database connection
var databaseCreateCmd = &cobra.Command{
	Use:   "create [company-id]",
	Short: "Create a database connection",
	Long:  `Create a new Visma database connection for a company`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		companyID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: invalid company ID: %s\n", args[0])
			os.Exit(1)
		}

		server, _ := cmd.Flags().GetString("server")
		instance, _ := cmd.Flags().GetString("instance")
		port, _ := cmd.Flags().GetInt("port")
		username, _ := cmd.Flags().GetString("username")
		password, _ := cmd.Flags().GetString("password")
		databaseName, _ := cmd.Flags().GetString("database")
		enabled, _ := cmd.Flags().GetBool("enabled")

		if server == "" || username == "" || password == "" || databaseName == "" {
			fmt.Println("Error: server, username, password, and database are required")
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		// Verify company exists
		companyService := services.NewCompanyService(repo)
		company, err := companyService.GetCompany(companyID)
		if err != nil {
			slog.Error("Failed to get company", "error", err)
			os.Exit(1)
		}

		if company == nil {
			fmt.Printf("Error: company with ID %d not found\n", companyID)
			os.Exit(1)
		}

		connection := &database.DatabaseConnection{
			CompanyID:    companyID,
			Server:       server,
			Instance:     instance,
			Port:         port,
			Username:     username,
			Password:     password,
			DatabaseName: databaseName,
			Params:       make(map[string]string),
			Enabled:      enabled,
		}

		connectionService := services.NewDatabaseConnectionService(repo)
		if err := connectionService.CreateDatabaseConnection(connection); err != nil {
			slog.Error("Failed to create database connection", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Database connection created successfully with ID: %d\n", connection.ID)
	},
}

// databaseUpdateCmd updates an existing database connection
var databaseUpdateCmd = &cobra.Command{
	Use:   "update [connection-id]",
	Short: "Update a database connection",
	Long:  `Update an existing database connection`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		connectionID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: invalid connection ID: %s\n", args[0])
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		connectionService := services.NewDatabaseConnectionService(repo)
		connection, err := connectionService.GetDatabaseConnection(connectionID)
		if err != nil {
			slog.Error("Failed to get database connection", "error", err)
			os.Exit(1)
		}

		if connection == nil {
			fmt.Printf("Error: database connection with ID %d not found\n", connectionID)
			os.Exit(1)
		}

		// Update fields if provided
		if cmd.Flags().Changed("server") {
			server, _ := cmd.Flags().GetString("server")
			connection.Server = server
		}
		if cmd.Flags().Changed("instance") {
			instance, _ := cmd.Flags().GetString("instance")
			connection.Instance = instance
		}
		if cmd.Flags().Changed("port") {
			port, _ := cmd.Flags().GetInt("port")
			connection.Port = port
		}
		if cmd.Flags().Changed("username") {
			username, _ := cmd.Flags().GetString("username")
			connection.Username = username
		}
		if cmd.Flags().Changed("password") {
			password, _ := cmd.Flags().GetString("password")
			connection.Password = password
		}
		if cmd.Flags().Changed("database") {
			databaseName, _ := cmd.Flags().GetString("database")
			connection.DatabaseName = databaseName
		}
		if cmd.Flags().Changed("enabled") {
			enabled, _ := cmd.Flags().GetBool("enabled")
			connection.Enabled = enabled
		}

		if err := connectionService.UpdateDatabaseConnection(connection); err != nil {
			slog.Error("Failed to update database connection", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Database connection %d updated successfully\n", connectionID)
	},
}

// databaseDeleteCmd deletes a database connection
var databaseDeleteCmd = &cobra.Command{
	Use:   "delete [connection-id]",
	Short: "Delete a database connection",
	Long:  `Delete a database connection`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		connectionID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: invalid connection ID: %s\n", args[0])
			os.Exit(1)
		}

		confirm, _ := cmd.Flags().GetBool("confirm")
		if !confirm {
			fmt.Printf("Error: use --confirm flag to confirm deletion of connection %d\n", connectionID)
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		connectionService := services.NewDatabaseConnectionService(repo)
		if err := connectionService.DeleteDatabaseConnection(connectionID); err != nil {
			slog.Error("Failed to delete database connection", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Database connection %d deleted successfully\n", connectionID)
	},
}

func init() {
	rootCmd.AddCommand(databaseCmd)
	databaseCmd.AddCommand(databaseListCmd)
	databaseCmd.AddCommand(databaseCreateCmd)
	databaseCmd.AddCommand(databaseUpdateCmd)
	databaseCmd.AddCommand(databaseDeleteCmd)

	// Create command flags
	databaseCreateCmd.Flags().String("server", "", "Database server (required)")
	databaseCreateCmd.Flags().String("instance", "", "Database instance")
	databaseCreateCmd.Flags().Int("port", 1433, "Database port")
	databaseCreateCmd.Flags().String("username", "", "Database username (required)")
	databaseCreateCmd.Flags().String("password", "", "Database password (required)")
	databaseCreateCmd.Flags().String("database", "", "Database name (required)")
	databaseCreateCmd.Flags().Bool("enabled", true, "Enable the connection")

	// Update command flags (same as create but optional)
	databaseUpdateCmd.Flags().String("server", "", "Database server")
	databaseUpdateCmd.Flags().String("instance", "", "Database instance")
	databaseUpdateCmd.Flags().Int("port", 1433, "Database port")
	databaseUpdateCmd.Flags().String("username", "", "Database username")
	databaseUpdateCmd.Flags().String("password", "", "Database password")
	databaseUpdateCmd.Flags().String("database", "", "Database name")
	databaseUpdateCmd.Flags().Bool("enabled", true, "Enable the connection")

	// Delete command flags
	databaseDeleteCmd.Flags().Bool("confirm", false, "Confirm deletion")
}
