package cmd

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/httpserver"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/vismadb"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"golang.org/x/exp/slog"
)

// runCmd represents the run command
var runCmd = &cobra.Command{
	Use:   "run",
	Short: "Runs this application i commandline mode",
	Long:  `Runs this application i commandline mode for debugging purposes`,
	Run: func(cmd *cobra.Command, args []string) {
		// Get Visma configuration from viper
		vismaServer := viper.GetString("visma.server")
		vismaInstance := viper.GetString("visma.instance")
		vismaPort := viper.GetInt("visma.port")
		vismaUsername := viper.GetString("visma.username")
		vismaPassword := viper.GetString("visma.password")
		vismaDatabase := viper.GetString("visma.database")
		vismaParams := viper.GetStringMapString("visma.params")

		// Log Visma configuration
		slog.Debug("Visma configuration", "server", vismaServer, "instance",
			vismaInstance, "port", vismaPort, "username", vismaUsername, "password",
			vismaPassword, "database", vismaDatabase, "params", vismaParams)

		client, err := vismadb.NewClient(&vismadb.ClientConfig{
			Server:   vismaServer,
			Instance: vismaInstance,
			Port:     vismaPort,
			Username: vismaUsername,
			Password: vismaPassword,
			Database: vismaDatabase,
			Params:   vismaParams,
		})
		if err != nil {
			slog.Error("error creating visma client", "error", err)
			os.Exit(1)
		}
		defer client.Close()

		viper.Set("visma.client", client)

		// Get HTTP server configuration from viper
		httpConfig := &httpserver.Config{
			Port: viper.GetInt("http.port"),
		}

		// Add database configuration
		httpConfig.Database = &httpserver.DatabaseConfig{
			Path:         viper.GetString("database.path"),
			MaxOpenConns: viper.GetInt("database.max_open_conns"),
			MaxIdleConns: viper.GetInt("database.max_idle_conns"),
		}

		// Add Keycloak configuration if enabled
		if viper.GetBool("keycloak.enabled") {
			// Build redirect URL
			redirectURL := fmt.Sprintf("http://localhost:%d/auth/callback", viper.GetInt("http.port"))
			if viper.GetString("keycloak.redirect_url") != "" {
				redirectURL = viper.GetString("keycloak.redirect_url")
			}

			httpConfig.Keycloak = &httpserver.KeycloakConfig{
				Enabled:      true,
				URL:          viper.GetString("keycloak.url"),
				Realm:        viper.GetString("keycloak.realm"),
				ClientID:     viper.GetString("keycloak.client_id"),
				ClientSecret: viper.GetString("keycloak.client_secret"),
				RequiredRole: viper.GetString("keycloak.required_role"),
				RedirectURL:  redirectURL,
			}

			// Add session configuration
			httpConfig.Session = &httpserver.SessionConfig{
				SecretKey: viper.GetString("session.secret_key"),
				MaxAge:    viper.GetInt("session.max_age"),
				Secure:    viper.GetBool("session.secure"),
				HttpOnly:  viper.GetBool("session.http_only"),
			}
		} else {
			// Use basic auth when Keycloak is not enabled
			httpConfig.BasicAuth = &httpserver.BasicAuthConfig{
				Username: viper.GetString("http.username"),
				Password: viper.GetString("http.password"),
			}
		}

		go func() {
			if err := httpserver.Start(httpConfig); err != nil {
				slog.Error("error starting http server", "error", err)
				os.Exit(1)
			}
		}()
		slog.Info("HTTP-server started", "port", httpConfig.Port)

		// This will catch CTRL+C, do cleanup and exit
		c := make(chan os.Signal, 2)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM, syscall.SIGKILL, syscall.SIGQUIT)

		//Wait until we get the quit message
		<-c
		slog.Info("Quitting...")
	},
}

func init() {
	rootCmd.AddCommand(runCmd)

	/* -- HTTP-server flags -- */
	runCmd.PersistentFlags().String("http-listen", "0.0.0.0", "The address to listen on for HTTP-traffic")
	viper.BindPFlag("http.listen", runCmd.PersistentFlags().Lookup("http-listen"))
	runCmd.PersistentFlags().Int("http-port", 8080, "The port to listen on for HTTP-traffic")
	viper.BindPFlag("http.port", runCmd.PersistentFlags().Lookup("http-port"))
	/* -- HTTP-server username and password -- */
	runCmd.PersistentFlags().String("http-username", "admin", "The username to use for HTTP-traffic")
	viper.BindPFlag("http.username", runCmd.PersistentFlags().Lookup("http-username"))
	runCmd.PersistentFlags().String("http-password", "admin", "The password to use for HTTP-traffic")
	viper.BindPFlag("http.password", runCmd.PersistentFlags().Lookup("http-password"))

	/* -- Database configuration -- */
	runCmd.PersistentFlags().String("database-path", "data/app.db", "Path to SQLite database file")
	viper.BindPFlag("database.path", runCmd.PersistentFlags().Lookup("database-path"))
	runCmd.PersistentFlags().Int("database-max-open-conns", 10, "Maximum number of open database connections")
	viper.BindPFlag("database.max_open_conns", runCmd.PersistentFlags().Lookup("database-max-open-conns"))
	runCmd.PersistentFlags().Int("database-max-idle-conns", 5, "Maximum number of idle database connections")
	viper.BindPFlag("database.max_idle_conns", runCmd.PersistentFlags().Lookup("database-max-idle-conns"))

	/* -- Visma configuration -- */
	runCmd.PersistentFlags().String("visma-server", "", "The server to use for Visma")
	viper.BindPFlag("visma.server", runCmd.PersistentFlags().Lookup("visma-server"))
	runCmd.PersistentFlags().String("visma-instance", "", "The instance to use for Visma")
	viper.BindPFlag("visma.instance", runCmd.PersistentFlags().Lookup("visma-instance"))
	runCmd.PersistentFlags().String("visma-username", "", "The username to use for Visma")
	viper.BindPFlag("visma.username", runCmd.PersistentFlags().Lookup("visma-username"))
	runCmd.PersistentFlags().String("visma-password", "", "The password to use for Visma")
	viper.BindPFlag("visma.password", runCmd.PersistentFlags().Lookup("visma-password"))
	runCmd.PersistentFlags().String("visma-companyname", "ERV Teknikk AS", "The company name to use for Visma")
	viper.BindPFlag("visma.companyname", runCmd.PersistentFlags().Lookup("visma-companyname"))
	runCmd.PersistentFlags().String("visma-database", "F0001", "The database to use for Visma")
	viper.BindPFlag("visma.database", runCmd.PersistentFlags().Lookup("visma-database"))
	runCmd.PersistentFlags().Int("visma-port", 1433, "The port to use for Visma")
	viper.BindPFlag("visma.port", runCmd.PersistentFlags().Lookup("visma-port"))

	/* -- Export configuration -- */
	runCmd.PersistentFlags().String("export-layout", "VLSTANDARD", "The layout to use for export")
	viper.BindPFlag("export.layout", runCmd.PersistentFlags().Lookup("export-layout"))
	runCmd.PersistentFlags().String("export-file", viper.GetString("appDir")+"\\go-lonn-exporter.csv", "The file to save export to")
	viper.BindPFlag("export.file", runCmd.PersistentFlags().Lookup("export-file"))
	runCmd.PersistentFlags().Bool("export-overwrite", false, "Overwrite the export file if it exists")
	viper.BindPFlag("export.overwrite", runCmd.PersistentFlags().Lookup("export-overwrite"))

	/* -- Keycloak configuration -- */
	runCmd.PersistentFlags().Bool("keycloak-enabled", false, "Enable Keycloak authentication")
	viper.BindPFlag("keycloak.enabled", runCmd.PersistentFlags().Lookup("keycloak-enabled"))
	runCmd.PersistentFlags().String("keycloak-url", "", "Keycloak server URL")
	viper.BindPFlag("keycloak.url", runCmd.PersistentFlags().Lookup("keycloak-url"))
	runCmd.PersistentFlags().String("keycloak-realm", "", "Keycloak realm")
	viper.BindPFlag("keycloak.realm", runCmd.PersistentFlags().Lookup("keycloak-realm"))
	runCmd.PersistentFlags().String("keycloak-client-id", "", "Keycloak client ID")
	viper.BindPFlag("keycloak.client_id", runCmd.PersistentFlags().Lookup("keycloak-client-id"))
	runCmd.PersistentFlags().String("keycloak-client-secret", "", "Keycloak client secret")
	viper.BindPFlag("keycloak.client_secret", runCmd.PersistentFlags().Lookup("keycloak-client-secret"))
	runCmd.PersistentFlags().String("keycloak-required-role", "lonnexport", "Required role for access")
	viper.BindPFlag("keycloak.required_role", runCmd.PersistentFlags().Lookup("keycloak-required-role"))
	runCmd.PersistentFlags().String("keycloak-redirect-url", "", "Keycloak redirect URL (defaults to http://localhost:PORT/auth/callback)")
	viper.BindPFlag("keycloak.redirect_url", runCmd.PersistentFlags().Lookup("keycloak-redirect-url"))

	/* -- Session configuration -- */
	runCmd.PersistentFlags().String("session-secret-key", "", "Session secret key (auto-generated if empty)")
	viper.BindPFlag("session.secret_key", runCmd.PersistentFlags().Lookup("session-secret-key"))
	runCmd.PersistentFlags().Int("session-max-age", 3600, "Session max age in seconds")
	viper.BindPFlag("session.max_age", runCmd.PersistentFlags().Lookup("session-max-age"))
	runCmd.PersistentFlags().Bool("session-secure", false, "Use secure session cookies (HTTPS only)")
	viper.BindPFlag("session.secure", runCmd.PersistentFlags().Lookup("session-secure"))
	runCmd.PersistentFlags().Bool("session-http-only", true, "Use HTTP-only session cookies")
	viper.BindPFlag("session.http_only", runCmd.PersistentFlags().Lookup("session-http-only"))
}
