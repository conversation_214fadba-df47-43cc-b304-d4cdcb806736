package cmd

import (
	"fmt"
	"log/slog"
	"os"
	"strconv"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/spf13/cobra"
)

// emailCmd represents the email command
var emailCmd = &cobra.Command{
	Use:   "email",
	Short: "Manage email mappings for Keycloak authentication",
	Long: `Manage email mappings that determine which Keycloak realm to use for authentication
based on user email addresses. Supports both domain patterns (@company.com) and 
specific email addresses (<EMAIL>).`,
}

// emailListCmd lists all email mappings
var emailListCmd = &cobra.Command{
	Use:   "list",
	Short: "List all email mappings",
	Long:  "List all email mappings configured in the database",
	Run: func(cmd *cobra.Command, args []string) {
		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		mappings, err := repo.GetEmailMappings()
		if err != nil {
			slog.Error("Failed to get email mappings", "error", err)
			os.Exit(1)
		}

		if len(mappings) == 0 {
			fmt.Println("No email mappings found")
			return
		}

		fmt.Printf("%-5s %-25s %-30s %-20s %-15s %-10s %-10s\n",
			"ID", "Pattern", "Description", "Realm", "URL", "Priority", "Enabled")
		fmt.Println("--------------------------------------------------------------------------------------------")

		for _, mapping := range mappings {
			enabled := "No"
			if mapping.Enabled {
				enabled = "Yes"
			}
			fmt.Printf("%-5d %-25s %-30s %-20s %-15s %-10d %-10s\n",
				mapping.ID, mapping.EmailPattern, mapping.Description,
				mapping.KeycloakRealm, mapping.KeycloakURL, mapping.Priority, enabled)
		}
	},
}

// emailAddCmd adds a new email mapping
var emailAddCmd = &cobra.Command{
	Use:   "add",
	Short: "Add a new email mapping",
	Long: `Add a new email mapping for Keycloak authentication.

Examples:
  # Add domain mapping
  erv-go-lonn-exporter email add --pattern "@company.com" --description "Company employees" --url "https://keycloak.company.com" --realm "company" --client-id "lonnexporter" --client-secret "secret123" --role "lonnexport" --priority 10

  # Add specific email mapping  
  erv-go-lonn-exporter email add --pattern "<EMAIL>" --description "Admin user" --url "https://keycloak.admin.com" --realm "admin" --client-id "lonnexporter" --client-secret "adminsecret" --role "admin" --priority 20`,
	Run: func(cmd *cobra.Command, args []string) {
		pattern, _ := cmd.Flags().GetString("pattern")
		description, _ := cmd.Flags().GetString("description")
		url, _ := cmd.Flags().GetString("url")
		realm, _ := cmd.Flags().GetString("realm")
		clientID, _ := cmd.Flags().GetString("client-id")
		clientSecret, _ := cmd.Flags().GetString("client-secret")
		role, _ := cmd.Flags().GetString("role")
		priority, _ := cmd.Flags().GetInt("priority")
		enabled, _ := cmd.Flags().GetBool("enabled")

		// Validate required fields
		if pattern == "" || description == "" || url == "" || realm == "" || clientID == "" || clientSecret == "" {
			fmt.Println("Error: pattern, description, url, realm, client-id, and client-secret are required")
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		mapping := &database.EmailMapping{
			EmailPattern:         pattern,
			Description:          description,
			KeycloakURL:          url,
			KeycloakRealm:        realm,
			KeycloakClientID:     clientID,
			KeycloakClientSecret: clientSecret,
			KeycloakRequiredRole: role,
			Priority:             priority,
			Enabled:              enabled,
		}

		if err := repo.CreateEmailMapping(mapping); err != nil {
			slog.Error("Failed to create email mapping", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Email mapping created successfully with ID: %d\n", mapping.ID)
	},
}

// emailUpdateCmd updates an existing email mapping
var emailUpdateCmd = &cobra.Command{
	Use:   "update [id]",
	Short: "Update an existing email mapping",
	Long:  "Update an existing email mapping by ID",
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		id, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: invalid ID '%s'\n", args[0])
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		// Get existing mapping
		mapping, err := repo.GetEmailMapping(id)
		if err != nil {
			slog.Error("Failed to get email mapping", "error", err)
			os.Exit(1)
		}

		if mapping == nil {
			fmt.Printf("Error: email mapping with ID %d not found\n", id)
			os.Exit(1)
		}

		// Update fields if provided
		if cmd.Flags().Changed("pattern") {
			pattern, _ := cmd.Flags().GetString("pattern")
			mapping.EmailPattern = pattern
		}
		if cmd.Flags().Changed("description") {
			description, _ := cmd.Flags().GetString("description")
			mapping.Description = description
		}
		if cmd.Flags().Changed("url") {
			url, _ := cmd.Flags().GetString("url")
			mapping.KeycloakURL = url
		}
		if cmd.Flags().Changed("realm") {
			realm, _ := cmd.Flags().GetString("realm")
			mapping.KeycloakRealm = realm
		}
		if cmd.Flags().Changed("client-id") {
			clientID, _ := cmd.Flags().GetString("client-id")
			mapping.KeycloakClientID = clientID
		}
		if cmd.Flags().Changed("client-secret") {
			clientSecret, _ := cmd.Flags().GetString("client-secret")
			mapping.KeycloakClientSecret = clientSecret
		}
		if cmd.Flags().Changed("role") {
			role, _ := cmd.Flags().GetString("role")
			mapping.KeycloakRequiredRole = role
		}
		if cmd.Flags().Changed("priority") {
			priority, _ := cmd.Flags().GetInt("priority")
			mapping.Priority = priority
		}
		if cmd.Flags().Changed("enabled") {
			enabled, _ := cmd.Flags().GetBool("enabled")
			mapping.Enabled = enabled
		}

		if err := repo.UpdateEmailMapping(mapping); err != nil {
			slog.Error("Failed to update email mapping", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Email mapping %d updated successfully\n", id)
	},
}

// emailDeleteCmd deletes an email mapping
var emailDeleteCmd = &cobra.Command{
	Use:   "delete [id]",
	Short: "Delete an email mapping",
	Long:  "Delete an email mapping by ID",
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		id, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: invalid ID '%s'\n", args[0])
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		// Verify mapping exists
		mapping, err := repo.GetEmailMapping(id)
		if err != nil {
			slog.Error("Failed to get email mapping", "error", err)
			os.Exit(1)
		}

		if mapping == nil {
			fmt.Printf("Error: email mapping with ID %d not found\n", id)
			os.Exit(1)
		}

		if err := repo.DeleteEmailMapping(id); err != nil {
			slog.Error("Failed to delete email mapping", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Email mapping %d deleted successfully\n", id)
	},
}

func init() {
	rootCmd.AddCommand(emailCmd)
	emailCmd.AddCommand(emailListCmd)
	emailCmd.AddCommand(emailAddCmd)
	emailCmd.AddCommand(emailUpdateCmd)
	emailCmd.AddCommand(emailDeleteCmd)

	// Add flags for email add command
	emailAddCmd.Flags().String("pattern", "", "Email pattern (@company.<NAME_EMAIL>)")
	emailAddCmd.Flags().String("description", "", "Description of this mapping")
	emailAddCmd.Flags().String("url", "", "Keycloak server URL")
	emailAddCmd.Flags().String("realm", "", "Keycloak realm")
	emailAddCmd.Flags().String("client-id", "", "Keycloak client ID")
	emailAddCmd.Flags().String("client-secret", "", "Keycloak client secret")
	emailAddCmd.Flags().String("role", "", "Required role (optional)")
	emailAddCmd.Flags().Int("priority", 0, "Priority (higher = checked first)")
	emailAddCmd.Flags().Bool("enabled", true, "Enable this mapping")

	// Add flags for email update command (same as add but optional)
	emailUpdateCmd.Flags().String("pattern", "", "Email pattern (@company.<NAME_EMAIL>)")
	emailUpdateCmd.Flags().String("description", "", "Description of this mapping")
	emailUpdateCmd.Flags().String("url", "", "Keycloak server URL")
	emailUpdateCmd.Flags().String("realm", "", "Keycloak realm")
	emailUpdateCmd.Flags().String("client-id", "", "Keycloak client ID")
	emailUpdateCmd.Flags().String("client-secret", "", "Keycloak client secret")
	emailUpdateCmd.Flags().String("role", "", "Required role (optional)")
	emailUpdateCmd.Flags().Int("priority", 0, "Priority (higher = checked first)")
	emailUpdateCmd.Flags().Bool("enabled", true, "Enable this mapping")
}
