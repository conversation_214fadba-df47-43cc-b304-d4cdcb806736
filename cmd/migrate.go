package cmd

import (
	"fmt"
	"log/slog"
	"os"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/services"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// migrateCmd represents the migrate command
var migrateCmd = &cobra.Command{
	Use:   "migrate",
	Short: "Migrate from single-tenant to multi-tenant configuration",
	Long: `Migrate existing single-tenant configuration to multi-tenant setup.
This command will:
1. Create a default company from existing Keycloak configuration
2. Create a database connection from existing Visma configuration
3. Preserve existing user settings and sessions`,
	Run: func(cmd *cobra.Command, args []string) {
		dryRun, _ := cmd.Flags().GetBool("dry-run")
		force, _ := cmd.Flags().GetBool("force")

		if dryRun {
			fmt.Println("DRY RUN MODE - No changes will be made")
			fmt.Println()
		}

		// Initialize repository
		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		companyService := services.NewCompanyService(repo)
		connectionService := services.NewDatabaseConnectionService(repo)

		// Check if migration is needed
		companies, err := companyService.GetCompanies()
		if err != nil {
			slog.Error("Failed to check existing companies", "error", err)
			os.Exit(1)
		}

		if len(companies) > 0 && !force {
			fmt.Printf("Migration not needed: %d companies already exist.\n", len(companies))
			fmt.Println("Use --force to migrate anyway (this will create additional companies).")
			return
		}

		// Get existing configuration
		keycloakConfig := getKeycloakConfig()
		vismaConfig := getVismaConfig()

		if keycloakConfig == nil {
			fmt.Println("Error: No Keycloak configuration found in config file")
			fmt.Println("Please ensure keycloak.* settings are configured")
			os.Exit(1)
		}

		if vismaConfig == nil {
			fmt.Println("Error: No Visma configuration found in config file")
			fmt.Println("Please ensure visma.* settings are configured")
			os.Exit(1)
		}

		fmt.Println("Found existing configuration:")
		fmt.Printf("  Keycloak URL: %s\n", keycloakConfig.URL)
		fmt.Printf("  Keycloak Realm: %s\n", keycloakConfig.Realm)
		fmt.Printf("  Keycloak Client ID: %s\n", keycloakConfig.ClientID)
		fmt.Printf("  Visma Server: %s\n", vismaConfig.Server)
		fmt.Printf("  Visma Database: %s\n", vismaConfig.Database)
		fmt.Printf("  Company Name: %s\n", vismaConfig.CompanyName)
		fmt.Println()

		if dryRun {
			fmt.Println("Would create:")
			fmt.Printf("  Company: %s (%s)\n", vismaConfig.CompanyName, "default")
			fmt.Printf("  Database Connection: %s -> %s\n", vismaConfig.Server, vismaConfig.Database)
			return
		}

		// Create default company
		company := &database.Company{
			Name:                 "default",
			DisplayName:          vismaConfig.CompanyName,
			KeycloakURL:          keycloakConfig.URL,
			KeycloakRealm:        keycloakConfig.Realm,
			KeycloakClientID:     keycloakConfig.ClientID,
			KeycloakClientSecret: keycloakConfig.ClientSecret,
			KeycloakRequiredRole: keycloakConfig.RequiredRole,
			Enabled:              true,
		}

		fmt.Println("Creating default company...")
		if err := companyService.CreateCompany(company); err != nil {
			slog.Error("Failed to create default company", "error", err)
			os.Exit(1)
		}
		fmt.Printf("✓ Created company: %s (ID: %d)\n", company.DisplayName, company.ID)

		// Create database connection
		connection := &database.DatabaseConnection{
			CompanyID:    company.ID,
			Server:       vismaConfig.Server,
			Instance:     vismaConfig.Instance,
			Port:         vismaConfig.Port,
			Username:     vismaConfig.Username,
			Password:     vismaConfig.Password,
			DatabaseName: vismaConfig.Database,
			Params:       make(map[string]string),
			Enabled:      true,
		}

		fmt.Println("Creating database connection...")
		if err := connectionService.CreateDatabaseConnection(connection); err != nil {
			slog.Error("Failed to create database connection", "error", err)
			os.Exit(1)
		}
		fmt.Printf("✓ Created database connection: %s -> %s (ID: %d)\n", 
			connection.Server, connection.DatabaseName, connection.ID)

		fmt.Println()
		fmt.Println("Migration completed successfully!")
		fmt.Println()
		fmt.Println("Next steps:")
		fmt.Println("1. Update your Keycloak configuration to include 'accessible_companies' custom claims")
		fmt.Println("2. Add company ID(s) to user tokens, e.g., \"accessible_companies\": [1]")
		fmt.Println("3. Restart the application to use multi-tenant mode")
		fmt.Printf("4. Access the application at the root URL to see company selection\n")
		fmt.Printf("5. Use 'company list' and 'database list' commands to manage configurations\n")
	},
}

// KeycloakConfig represents Keycloak configuration from viper
type KeycloakConfig struct {
	URL          string
	Realm        string
	ClientID     string
	ClientSecret string
	RequiredRole string
}

// VismaConfig represents Visma configuration from viper
type VismaConfig struct {
	Server      string
	Instance    string
	Port        int
	Username    string
	Password    string
	Database    string
	CompanyName string
}

// getKeycloakConfig extracts Keycloak configuration from viper
func getKeycloakConfig() *KeycloakConfig {
	if !viper.IsSet("keycloak.url") {
		return nil
	}

	return &KeycloakConfig{
		URL:          viper.GetString("keycloak.url"),
		Realm:        viper.GetString("keycloak.realm"),
		ClientID:     viper.GetString("keycloak.clientid"),
		ClientSecret: viper.GetString("keycloak.clientsecret"),
		RequiredRole: viper.GetString("keycloak.requiredrole"),
	}
}

// getVismaConfig extracts Visma configuration from viper
func getVismaConfig() *VismaConfig {
	if !viper.IsSet("visma.server") {
		return nil
	}

	port := viper.GetInt("visma.port")
	if port == 0 {
		port = 1433 // Default SQL Server port
	}

	companyName := viper.GetString("visma.companyname")
	if companyName == "" {
		companyName = "Default Company"
	}

	return &VismaConfig{
		Server:      viper.GetString("visma.server"),
		Instance:    viper.GetString("visma.instance"),
		Port:        port,
		Username:    viper.GetString("visma.username"),
		Password:    viper.GetString("visma.password"),
		Database:    viper.GetString("visma.database"),
		CompanyName: companyName,
	}
}

func init() {
	rootCmd.AddCommand(migrateCmd)

	migrateCmd.Flags().Bool("dry-run", false, "Show what would be migrated without making changes")
	migrateCmd.Flags().Bool("force", false, "Force migration even if companies already exist")
}
