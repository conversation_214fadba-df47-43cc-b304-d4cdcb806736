package cmd

import (
	"fmt"
	"log/slog"
	"os"

	"github.com/probits-as/erv-go-lonn-exporter/internal/pkg/database"
	"github.com/spf13/cobra"
)

// keycloakCmd represents the keycloak command
var keycloakCmd = &cobra.Command{
	Use:   "keycloak",
	Short: "Manage default Keycloak configuration",
	Long: `Manage the default Keycloak configuration that is used as a fallback
when no specific email mapping is found for a user's email address.`,
}

// keycloakShowCmd shows the current default Keycloak configuration
var keycloakShowCmd = &cobra.Command{
	Use:   "show",
	Short: "Show current default Keycloak configuration",
	Long:  "Display the current default Keycloak configuration",
	Run: func(cmd *cobra.Command, args []string) {
		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		config, err := repo.GetDefaultKeycloakConfig()
		if err != nil {
			slog.Error("Failed to get default Keycloak config", "error", err)
			os.Exit(1)
		}

		if config == nil {
			fmt.Println("No default Keycloak configuration found")
			return
		}

		fmt.Printf("Default Keycloak Configuration:\n")
		fmt.Printf("  ID:            %d\n", config.ID)
		fmt.Printf("  URL:           %s\n", config.KeycloakURL)
		fmt.Printf("  Realm:         %s\n", config.KeycloakRealm)
		fmt.Printf("  Client ID:     %s\n", config.KeycloakClientID)
		fmt.Printf("  Client Secret: %s\n", maskSecret(config.KeycloakClientSecret))
		fmt.Printf("  Required Role: %s\n", config.KeycloakRequiredRole)
		fmt.Printf("  Enabled:       %t\n", config.Enabled)
		fmt.Printf("  Created:       %s\n", config.CreatedAt.Format("2006-01-02 15:04:05"))
		fmt.Printf("  Updated:       %s\n", config.UpdatedAt.Format("2006-01-02 15:04:05"))
	},
}

// keycloakSetCmd sets the default Keycloak configuration
var keycloakSetCmd = &cobra.Command{
	Use:   "set",
	Short: "Set default Keycloak configuration",
	Long: `Set the default Keycloak configuration that will be used as a fallback
when no specific email mapping is found.

Example:
  erv-go-lonn-exporter keycloak set --url "https://keycloak.example.com" --realm "default" --client-id "lonnexporter" --client-secret "secret123" --role "lonnexport"`,
	Run: func(cmd *cobra.Command, args []string) {
		url, _ := cmd.Flags().GetString("url")
		realm, _ := cmd.Flags().GetString("realm")
		clientID, _ := cmd.Flags().GetString("client-id")
		clientSecret, _ := cmd.Flags().GetString("client-secret")
		role, _ := cmd.Flags().GetString("role")
		enabled, _ := cmd.Flags().GetBool("enabled")

		// Validate required fields
		if url == "" || realm == "" || clientID == "" || clientSecret == "" {
			fmt.Println("Error: url, realm, client-id, and client-secret are required")
			os.Exit(1)
		}

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		config := &database.DefaultKeycloakConfig{
			KeycloakURL:          url,
			KeycloakRealm:        realm,
			KeycloakClientID:     clientID,
			KeycloakClientSecret: clientSecret,
			KeycloakRequiredRole: role,
			Enabled:              enabled,
		}

		if err := repo.SetDefaultKeycloakConfig(config); err != nil {
			slog.Error("Failed to set default Keycloak config", "error", err)
			os.Exit(1)
		}

		fmt.Println("Default Keycloak configuration set successfully")
	},
}

// keycloakTestCmd tests an email against the current configuration
var keycloakTestCmd = &cobra.Command{
	Use:   "test [email]",
	Short: "Test which Keycloak configuration would be used for an email",
	Long:  "Test which Keycloak configuration (email mapping or default) would be used for a given email address",
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		email := args[0]

		repo, err := initRepository()
		if err != nil {
			slog.Error("Failed to initialize repository", "error", err)
			os.Exit(1)
		}

		fmt.Printf("Testing email: %s\n\n", email)

		// Check for email mapping first
		mapping, err := repo.GetEmailMappingForEmail(email)
		if err != nil {
			slog.Error("Failed to get email mapping", "error", err)
			os.Exit(1)
		}

		if mapping != nil {
			fmt.Printf("✓ Found email mapping (ID: %d)\n", mapping.ID)
			fmt.Printf("  Pattern:       %s\n", mapping.EmailPattern)
			fmt.Printf("  Description:   %s\n", mapping.Description)
			fmt.Printf("  URL:           %s\n", mapping.KeycloakURL)
			fmt.Printf("  Realm:         %s\n", mapping.KeycloakRealm)
			fmt.Printf("  Client ID:     %s\n", mapping.KeycloakClientID)
			fmt.Printf("  Client Secret: %s\n", maskSecret(mapping.KeycloakClientSecret))
			fmt.Printf("  Required Role: %s\n", mapping.KeycloakRequiredRole)
			fmt.Printf("  Priority:      %d\n", mapping.Priority)
			fmt.Printf("  Enabled:       %t\n", mapping.Enabled)
			return
		}

		// Check default configuration
		defaultConfig, err := repo.GetDefaultKeycloakConfig()
		if err != nil {
			slog.Error("Failed to get default Keycloak config", "error", err)
			os.Exit(1)
		}

		if defaultConfig != nil {
			fmt.Printf("→ Using default Keycloak configuration\n")
			fmt.Printf("  URL:           %s\n", defaultConfig.KeycloakURL)
			fmt.Printf("  Realm:         %s\n", defaultConfig.KeycloakRealm)
			fmt.Printf("  Client ID:     %s\n", defaultConfig.KeycloakClientID)
			fmt.Printf("  Client Secret: %s\n", maskSecret(defaultConfig.KeycloakClientSecret))
			fmt.Printf("  Required Role: %s\n", defaultConfig.KeycloakRequiredRole)
			fmt.Printf("  Enabled:       %t\n", defaultConfig.Enabled)
			return
		}

		fmt.Printf("✗ No configuration found for this email\n")
		fmt.Printf("  - No email mapping matches\n")
		fmt.Printf("  - No default configuration set\n")
		fmt.Printf("  - Authentication will fail\n")
	},
}

// maskSecret masks a secret string for display
func maskSecret(secret string) string {
	if len(secret) <= 4 {
		return "****"
	}
	return secret[:2] + "****" + secret[len(secret)-2:]
}

func init() {
	rootCmd.AddCommand(keycloakCmd)
	keycloakCmd.AddCommand(keycloakShowCmd)
	keycloakCmd.AddCommand(keycloakSetCmd)
	keycloakCmd.AddCommand(keycloakTestCmd)

	// Add flags for keycloak set command
	keycloakSetCmd.Flags().String("url", "", "Keycloak server URL")
	keycloakSetCmd.Flags().String("realm", "", "Keycloak realm")
	keycloakSetCmd.Flags().String("client-id", "", "Keycloak client ID")
	keycloakSetCmd.Flags().String("client-secret", "", "Keycloak client secret")
	keycloakSetCmd.Flags().String("role", "", "Required role (optional)")
	keycloakSetCmd.Flags().Bool("enabled", true, "Enable this configuration")
}
