package cmd

import (
	"fmt"

	"github.com/probits-as/erv-go-lonn-exporter/internal/app"
	"github.com/spf13/cobra"
)

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Print the version information for this application",
	Long:  app.SummaryTitle,
	RunE: func(cmd *cobra.Command, args []string) error {
		fmt.Println(app.ShortTitle + "\n" + app.SummaryTitle)
		return nil
	},
	Example: versionExample(),
}

func versionExample() string {
	return `$ erv-go-lonn-exporter version`
}

func init() {
	// Add this command to the root-command
	rootCmd.AddCommand(versionCmd)
}
