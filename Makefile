APPNAME = $(shell basename "${PWD}")
IMPORTPATH = $(shell pwd | rev | awk -F'/' 'BEGIN { OFS = "/" }{print $$1,$$2,$$3}' | rev)
TEMPLATEIMPORTPATH = github.com/fkmeland/go-projecttemplate

GOCMD = go
GOARCH = amd64
PLATFORMS := windows linux darwin
os = $(word 1, $@)

VERSION=$(shell git describe --tags --always)
#VERSION="v0.0.1"
COMMIT=$(shell git rev-parse HEAD)
BRANCH=$(shell git rev-parse --abbrev-ref HEAD)
BUILD=$(shell date +%FT%T%z)
$(eval GOVERSION=$(shell sh -c "$(GOCMD) version" | awk '{print $$3}'))

PROJECT_DIR=$(shell pwd)
BUILD_DIR=${PROJECT_DIR}/bin

# Setup the -ldflags option for go build here, interpolate the variable values
## Static link (can`t remember why this was needed)
#LDFLAGS = -ldflags "-linkmode external -extldflags -static -X main.appName=${APPNAME} -X main.appVersion=${VERSION} -X main.appCommit=${COMMIT} -X main.appBranch=${BRANCH} -X main.appBuild=${BUILD} -X main.goVersion=${GOVERSION}"
## Dynamic link (default)
LDFLAGS = -ldflags "-X main.appName=${APPNAME} -X main.appVersion=${VERSION} -X main.appCommit=${COMMIT} -X main.appBranch=${BRANCH} -X main.appBuild=${BUILD} -X main.goVersion=${GOVERSION}"

# Build the project
all: linux darwin windows init

.PHONY: $(PLATFORMS)
$(PLATFORMS):
	@if [ ! -d "${BUILD_DIR}" ]; then \
		echo "Creating ${BUILD_DIR}"; \
		mkdir -p ${BUILD_DIR}; \
	fi; \
	cd ${PROJECT_DIR}; \
	echo "Starting go build..."; \
	GOOS=$(os) GOARCH=${GOARCH} $(GOCMD) build ${LDFLAGS} -o ${BUILD_DIR}/${APPNAME}_$(os)_${GOARCH} . ; \
	if [ $$? != 0 ]; then \
		echo "\nBuild failed"; \
		exit $$?; \
	fi; \
	echo "Build successful..."; \
	if [ -d "${PROJECT_DIR}/conf" ]; then \
		if [ ! -d "$(BUILD_DIR)/conf" ]; then \
			echo "Copying example configs"; \
			cp -R $(PROJECT_DIR)/conf $(BUILD_DIR)/; \
		fi; \
	fi; \
	if [ -d "$(PROJECT_DIR)/public" ]; then \
		if [ ! -d "$(BUILD_DIR)/public" ]; then \
			echo "Creating  $(BUILD_DIR)/public"; \
			mkdir $(BUILD_DIR)/public; \
		fi; \
		echo "Syncing public-folder"; \
		rsync -aqrzh --delete  $(PROJECT_DIR)/public/ $(BUILD_DIR)/public; \
	fi; \
	cd - >/dev/null

init:
	# Fixes import-paths in go-code
	-find . -type f -not -path "./vendor/*" -name "*.go" -exec sed -i 's+${TEMPLATEIMPORTPATH}+${IMPORTPATH}+g' {} +
	-sed -i 's+${TEMPLATEIMPORTPATH}+${IMPORTPATH}+g' ${PROJECT_DIR}/go.mod

docker:
	APPNAME=${APPNAME} APPVERSION=${VERSION} COMMIT=${COMMIT} BRANCH=${BRANCH} BUILD=${BUILD} GOVERSION=${GOVERSION} docker-compose build

testrelease:
	GOVERSION=${GOVERSION} goreleaser --snapshot --rm-dist

release:
	GOVERSION=${GOVERSION} goreleaser --rm-dist

clean:
	-rm -rf ${BUILD_DIR}
	-rm -rf ${DIST_DIR}

.PHONY: linux darwin windows docker release testrelease clean init